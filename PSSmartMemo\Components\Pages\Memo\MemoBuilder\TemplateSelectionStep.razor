@using PSSmartMemo.DTO
@using Syncfusion.Blazor.DropDowns
@using Syncfusion.Blazor.Inputs
@inject MemoDataService MemoDataService
@inject AuthenticationStateProvider AuthenticationStateProvider

<div class="template-selection-step">
    <div class="step-header">
        <h2 class="step-title">
            <MudIcon Icon="@Icons.Material.Filled.Description" Class="step-icon" />
            Template Selection & Memo Title
        </h2>
        <p class="step-description">
            Choose a memo template and provide a descriptive title for your memo. Both fields are required to proceed.
        </p>
    </div>

    <div class="step-content">
        <div class="form-section">
            <div class="form-group">
                <label class="form-label">
                    <MudIcon Icon="@Icons.Material.Filled.Assignment" Size="Size.Small" />
                    Select Template *
                </label>
                <SfDropDownList DataSource="@_availableTemplates"
                                Placeholder="Choose a memo template..."
                                @bind-Value="@_selectedTemplateId"
                                TValue="int?"
                                TItem="TemplateDto"
                                AllowFiltering="true"
                                FilterType="FilterType.Contains"
                                PopupWidth="100%"
                                FloatLabelType="FloatLabelType.Never"
                                CssClass="template-dropdown">
                    <DropDownListFieldSettings Value="@nameof(TemplateDto.TemplateId)"
                                               Text="@nameof(TemplateDto.TemplateTitle)">
                    </DropDownListFieldSettings>
                    <DropDownListEvents TValue="int?" TItem="TemplateDto" ValueChange="OnTemplateChanged"></DropDownListEvents>
                </SfDropDownList>
                @if (_showTemplateError)
                {
                    <div class="validation-error">
                        <MudIcon Icon="@Icons.Material.Filled.Error" Size="Size.Small" />
                        Please select a template to continue.
                    </div>
                }
            </div>

            @if (_selectedTemplate != null)
            {
                <div class="template-info">
                    <div class="template-info-header">
                        <MudIcon Icon="@Icons.Material.Filled.Info" Color="Color.Primary" />
                        <span>Template Information</span>
                    </div>
                    <div class="template-details">
                        <div class="template-detail-item">
                            <strong>Type:</strong> @_selectedTemplate.MemoType
                        </div>
                        <div class="template-detail-item">
                            <strong>Sections:</strong> @(_selectedTemplate.Sections?.Count ?? 0) sections
                        </div>
                        @if (_selectedTemplate.MemoTemplateAttachmentAllowed)
                        {
                            <div class="template-detail-item">
                                <strong>Attachments:</strong> Allowed 
                                (@(_selectedTemplate.MemoTemplateAttachmentFileCountAllowed) files max, 
                                @(_selectedTemplate.MemoTemplateAttachmentPerFileSizeMbAllowed)MB per file)
                            </div>
                        }
                        else
                        {
                            <div class="template-detail-item">
                                <strong>Attachments:</strong> Not allowed
                            </div>
                        }
                    </div>
                </div>
            }

            <div class="form-group">
                <label class="form-label">
                    <MudIcon Icon="@Icons.Material.Filled.Title" Size="Size.Small" />
                    Memo Title *
                </label>
                <SfTextBox @bind-Value="@_memoTitle"
                           Placeholder="Enter a descriptive title for your memo..."
                           FloatLabelType="FloatLabelType.Never"
                           CssClass="memo-title-input"
                           Multiline="false">
                    <TextBoxEvents ValueChange="@OnTitleInputChanged"></TextBoxEvents>
                </SfTextBox>
                @if (_showTitleError)
                {
                    <div class="validation-error">
                        <MudIcon Icon="@Icons.Material.Filled.Error" Size="Size.Small" />
                        Please enter a memo title to continue.
                    </div>
                }
            </div>
        </div>

        <div class="step-actions">
            <div class="validation-summary">
                @if (_isValid)
                {
                    <div class="validation-success">
                        <MudIcon Icon="@Icons.Material.Filled.CheckCircle" Color="Color.Success" />
                        Ready to proceed to content composition
                    </div>
                }
                else
                {
                    <div class="validation-info">
                        <MudIcon Icon="@Icons.Material.Filled.Info" Color="Color.Info" />
                        Complete both fields above to continue
                    </div>
                }
            </div>
        </div>
    </div>
</div>

@code {
    [Parameter] public EventCallback<bool> OnStepCompleted { get; set; }
    [Parameter] public EventCallback<int?> OnTemplateSelected { get; set; }
    [Parameter] public EventCallback<string> OnTitleChanged { get; set; }
    [Parameter] public int? SelectedTemplateId { get; set; }
    [Parameter] public string MemoTitle { get; set; } = "";

    private List<TemplateDto> _availableTemplates = new();
    private int? _selectedTemplateId;
    private string _memoTitle = "";
    private TemplateDto? _selectedTemplate;
    private bool _isValid = false;
    private bool _showTemplateError = false;
    private bool _showTitleError = false;
    private string _currentUserId = "";

    protected override async Task OnInitializedAsync()
    {
        var authState = await AuthenticationStateProvider.GetAuthenticationStateAsync();
        _currentUserId = authState.User.Identity?.Name ?? "";
        
        await LoadAvailableTemplates();
        
        // Initialize with passed parameters
        _selectedTemplateId = SelectedTemplateId;
        _memoTitle = MemoTitle ?? "";
        
        if (_selectedTemplateId.HasValue)
        {
            _selectedTemplate = _availableTemplates.FirstOrDefault(t => t.TemplateId == _selectedTemplateId.Value);
        }
        
        ValidateStep();
    }

    protected override async Task OnParametersSetAsync()
    {
        if (_selectedTemplateId != SelectedTemplateId)
        {
            _selectedTemplateId = SelectedTemplateId;
            if (_selectedTemplateId.HasValue)
            {
                _selectedTemplate = _availableTemplates.FirstOrDefault(t => t.TemplateId == _selectedTemplateId.Value);
            }
        }
        
        if (_memoTitle != MemoTitle)
        {
            _memoTitle = MemoTitle ?? "";
        }
        
        ValidateStep();
    }

    private async Task LoadAvailableTemplates()
    {
        try
        {
            _availableTemplates = await MemoDataService.GetAssignTemplates(_currentUserId);
        }
        catch (Exception ex)
        {
            // Handle error - could show toast notification
            _availableTemplates = new List<TemplateDto>();
        }
    }

    private async Task OnTemplateChanged(ChangeEventArgs<int?, TemplateDto> args)
    {
        _selectedTemplateId = args.Value;
        _selectedTemplate = args.ItemData;
        _showTemplateError = false;
        
        await OnTemplateSelected.InvokeAsync(_selectedTemplateId);
        ValidateStep();
    }

    private async Task OnTitleInputChanged()
    {
        _showTitleError = false;
        await OnTitleChanged.InvokeAsync(_memoTitle);
        ValidateStep();
    }

    private void ValidateStep()
    {
        _isValid = _selectedTemplateId.HasValue && !string.IsNullOrWhiteSpace(_memoTitle);
        OnStepCompleted.InvokeAsync(_isValid);
    }

    public void ShowValidationErrors()
    {
        _showTemplateError = !_selectedTemplateId.HasValue;
        _showTitleError = string.IsNullOrWhiteSpace(_memoTitle);
        StateHasChanged();
    }
}

<style>
    .template-selection-step {
        max-width: 800px;
        margin: 0 auto;
        padding: 2rem;
    }

    .step-header {
        margin-bottom: 2rem;
        text-align: center;
    }

    .step-title {
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 0.5rem;
        font-size: 1.5rem;
        font-weight: 600;
        color: #2c3e50;
        margin-bottom: 0.5rem;
    }

    .step-icon {
        color: #4f8cff;
    }

    .step-description {
        color: #6c757d;
        font-size: 1rem;
        line-height: 1.5;
        margin: 0;
    }

    .step-content {
        background: #fff;
        border-radius: 12px;
        padding: 2rem;
        box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
        border: 1px solid #e9ecef;
    }

    .form-section {
        margin-bottom: 2rem;
    }

    .form-group {
        margin-bottom: 1.5rem;
    }

    .form-label {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        font-weight: 600;
        color: #2c3e50;
        margin-bottom: 0.5rem;
        font-size: 0.9rem;
    }

    .template-dropdown,
    .memo-title-input {
        width: 100%;
    }

    .template-info {
        background: #f8f9fa;
        border: 1px solid #e9ecef;
        border-radius: 8px;
        padding: 1rem;
        margin-top: 1rem;
    }

    .template-info-header {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        font-weight: 600;
        color: #4f8cff;
        margin-bottom: 0.75rem;
    }

    .template-details {
        display: grid;
        gap: 0.5rem;
    }

    .template-detail-item {
        color: #495057;
        font-size: 0.9rem;
    }

    .validation-error {
        display: flex;
        align-items: center;
        gap: 0.25rem;
        color: #dc3545;
        font-size: 0.8rem;
        margin-top: 0.25rem;
    }

    .step-actions {
        border-top: 1px solid #e9ecef;
        padding-top: 1.5rem;
    }

    .validation-summary {
        text-align: center;
    }

    .validation-success,
    .validation-info {
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 0.5rem;
        padding: 0.75rem;
        border-radius: 6px;
        font-weight: 500;
    }

    .validation-success {
        background: #d4edda;
        color: #155724;
        border: 1px solid #c3e6cb;
    }

    .validation-info {
        background: #d1ecf1;
        color: #0c5460;
        border: 1px solid #bee5eb;
    }
</style>
