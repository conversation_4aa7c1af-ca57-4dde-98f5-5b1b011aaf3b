﻿using System.Data;
using System.Web;
using Dapper;

namespace PSSmartMemo.Services;

public class MemoDataService(
    IDbContextFactory<ApplicationDbContext> contextFactory,
    HttpClient http,
    IConfiguration configuration,
    CorporateService corporateService
)
{
    public async Task<List<MemoDto>> GetMyMemos(string loginId)
    {
        var dc = await contextFactory.CreateDbContextAsync();
        var q = (from a in dc.Memos
            where a.MemoCreatedBy == loginId &&
                  a.MemoIsActive == true &&
                  a.MemoIsDel == false &&
                  (a.MemoStatus == "PUBLISHED" || a.MemoStatus == "DRAFT")
            orderby a.MemoCreatedDate descending
            select new MemoDto
            {
                MemoId = a.MemoId,
                MemoCode = a.MemoCode,
                MemoTitle = a.MemoTitle,
                MemoStatus = a.MemoStatus,
                MemoTypeId = a.MemoTypeId,
                MemoTemplateId = a.MemoTemplateId,
                MemoCreatedDate = a.MemoCreatedDate,
                MemoTypeStr = a.MemoTemplate.MemoType.MemoTypeName,
                CanDelete = !dc.MemoApprovalLogs.Any(m => m.MemoId == a.MemoId)
            }).ToList();
        MemoDto newMemo = new()
        {
            MemoId = -1,
            MemoCode = "010101000101",
            MemoTitle = "------- NEW MEMO -------",
            MemoStatus = "",
            MemoTypeId = 1,
            MemoTemplateId = -1,
            MemoCreatedDate = DateTime.Now.AddDays(30)
        };
        q.Add(newMemo);
        //q = q.OrderBy(x => x.MemoCreatedDate).OrderDescending().ToList();
        return await Task.FromResult(q);
    }

    public async Task<List<TemplateDto>> GetAssignTemplates(string loginId)
    {
        var dc = await contextFactory.CreateDbContextAsync();
        var q = (from a in dc.MemoTemplates
            join b in dc.MemoTemplateUserRoleAssigns on a.MemoTemplateId equals b.MemoTemplateId
            join c in dc.UserRoles on b.RoleId equals c.RoleId
            join d in dc.Users on c.UserId equals d.Id
            where d.UserId == loginId &&
                  a.MemoTemplateIsActive == true &&
                  a.MemoTemplateIsDel == false &&
                  a.MemoTemplateStatus == 2 // published only memo templates
            select new TemplateDto
            {
                TemplateId = a.MemoTemplateId,
                MemoTemplateCode = a.MemoTemplateCode,
                MemoTemplatePrefixCode = a.MemoTemplatePrefixCode,
                TemplateTitle = $"{a.MemoTemplateTitle} - ({a.MemoType.MemoTypeName})",
                MemoTemplateAttachmentAllowed = a.MemoTemplateAttachmentAllowed == true,
                MemoTemplateAttachmentPerFileSizeMbAllowed = a.MemoTemplateAttachmentPerFileSizeMballowed ?? 0,
                MemoTemplateAttachmentFileCountAllowed = (int)(a.MemoTemplateAttachmentFileCountAllowed ?? 0)
            }).Distinct().ToList();
        return await Task.FromResult(q);
    }

    public async Task<List<TemplateSectionDto>> GetTemplateSection(int templateId)
    {
        var dc = await contextFactory.CreateDbContextAsync();
        var q = (from b in dc.MemoTemplateSections
            where b.MemoTemplateId == templateId
            orderby b.MemoTemplateSectionSortOrder
            select new TemplateSectionDto
            {
                ContentHtml = b.MemoTemplateSectionContentHtml,
                IsRequired = b.MemoTemplateSectionIsRequired,
                SectionTitle = b.MemoTemplateSectionTitle,
                TemplateSectionId = b.MemoTemplateSectionId,
                SectionSortOrder = b.MemoTemplateSectionSortOrder,
                isOpen = false
            }).ToList();
        return await Task.FromResult(q);
    }

    public async Task<MemoDto> GetMemo(int memoId)
    {
        var dc = await contextFactory.CreateDbContextAsync();
        var q = (from a in dc.Memos
            where a.MemoId == memoId
            select new MemoDto
            {
                MemoId = a.MemoId,
                MemoTemplateId = a.MemoTemplateId,
                MemoTitle = a.MemoTitle,
                MemoStatus = a.MemoStatus,
                MemoCreatedBy = a.MemoCreatedBy,
                MemoCreatedDate = a.MemoCreatedDate,
                MemoModifiedBy = a.MemoModifiedBy,
                MemoModifiedDate = a.MemoModifiedDate,
                MemoIsActive = a.MemoIsActive,
                MemoTypeId = a.MemoTypeId,
                MemoStatusId = a.MemoStatusId ?? 1
            }).FirstOrDefault();
        return await Task.FromResult(q!);
    }

    public async Task<List<MemoSectionDto>> GetMemoSections(int memoId, MemoDto memo)
    {
        var dc = await contextFactory.CreateDbContextAsync();
        var q = (from b in dc.MemoTemplateSections
            where b.MemoTemplateId == memo.MemoTemplateId && b.MemoTemplateSectionIsDel == false &&
                  b.MemoTemplateSectionIsActive == true
            orderby b.MemoTemplateSectionSortOrder
            select new MemoSectionDto
            {
                MemoId = memoId,
                TemplateSectionTitle = b.MemoTemplateSectionTitle,
                TemplateContentHtml = b.MemoTemplateSectionContentHtml,
                MemoTemplateSectionId = b.MemoTemplateSectionId,
                IsRequired = b.MemoTemplateSectionIsRequired,
                SectionSortOrder = b.MemoTemplateSectionSortOrder,
                IgnoreSection = !b.MemoTemplateSectionIsRequired,
                isOpen = false,
                ContentHtml = b.MemoTemplateSectionIsWaterMark ? "" : b.MemoTemplateSectionContentHtml,
                MemoIsActive = false,
                Placeholder = b.MemoTemplateSectionContentHtml,
                IsWaterMark = b.MemoTemplateSectionIsWaterMark,
                
            }).ToList();
        
        foreach (var i in q)
            if (i.IsWaterMark == false)
                i.ContentHtml = i.Placeholder;

        var memoSecExists = (from a in dc.MemoSections where a.MemoId == memoId select a).Any();
        if (memoSecExists)
            foreach (var item in q)
            {
                var memoSections = (from a in dc.MemoSections
                    where a.MemoId == memoId &&
                          a.MemoTemplateSectionId == item.MemoTemplateSectionId
                          && a.MemoSectionIsDel == false && a.MemoSectionIsActive == true
                    select new {
                        a.MemoSectionContentHtml,
                        a.MemoSectionId,
                        a.MemoSectionIgnored,
                        a.MemoIsEncrypted,
                        a.MemoSectionContentText
                    }).FirstOrDefault();

                if (memoSections != null)
                {
                    if (memoSections.MemoIsEncrypted)
                    {
                        // Set the encrypted content and encryption flag
                        item.SetEncryptedContent(memoSections.MemoSectionContentHtml, memoSections.MemoIsEncrypted);
                        item.SetEncryptedTextContent(memoSections.MemoSectionContentText, memoSections.MemoIsEncrypted);

                        item.MemoIsEncrypted = memoSections.MemoIsEncrypted;
                        item.MemoIsActive = true;
                        item.Id = memoSections.MemoSectionId;
                        item.IgnoreSection = memoSections.MemoSectionIgnored;
                    }
                    else
                    {
                        //item.SetEncryptedContent(memoSections.MemoSectionContentHtml, memoSections.MemoIsEncrypted);
                        //item.SetEncryptedTextContent(memoSections.MemoSectionContentText, memoSections.MemoIsEncrypted);
                        item.ContentHtml = memoSections.MemoSectionContentHtml;
                        item.MemoIsEncrypted = memoSections.MemoIsEncrypted;
                        item.MemoIsActive = true;
                        item.Id = memoSections.MemoSectionId;
                        item.IgnoreSection = memoSections.MemoSectionIgnored;
                    }
                }
            }

        return await Task.FromResult(q);
    }

    public async Task<List<MemoAttachmentDto>> GetMemoAttachments(int memoId)
    {
        var dc = await contextFactory.CreateDbContextAsync();
        var q = (from a in dc.MemoAttachments
            where a.MemoId == memoId && a.MemoAttachmentIsDel == false && a.MemoAttachmentIsActive == true
            select new MemoAttachmentDto
            {
                AttachmentTypeId = a.AttachmentTypeId,
                Name = a.MemoAttachmentDocName,
                Path = AppDataService.ConvertToUrl(a.MemoAttachmentFilePath),
                Type = a.MemoAttachmentDocType,
                Size = a.MemoAttachmentDocSizeMb.ToString(),
                AttachmentType = a.AttachmentType.AttachmentTypeTitle,
                Description = a.MemoAttachmentDescription
            }).ToList();
        return await Task.FromResult(q);
    }

    public async Task<int> SaveMemo_2(int memoId, MemoDto memoDto, List<MemoSectionDto> memoSections,
        List<MemoAttachmentDto> memoAttachments, List<MemoApproverDto> memoApprovers, string user, string memoStatus,
        bool isLogAtObjectState)
    {
        var dc = await contextFactory.CreateDbContextAsync();
        var userCode = GetCodeOnly(user);
        var deptCode = GetDepartmentNickByEmployeeNo(dc, userCode);
        if (string.IsNullOrEmpty(deptCode))
            return 0;
        var templateCode = dc.MemoTemplates.FirstOrDefault(c => c.MemoTemplateId == memoDto.MemoTemplateId)!
            .MemoTemplateCode ?? "";
        var refCode = Get_RefCode(dc, user, templateCode, deptCode);
        if (string.IsNullOrEmpty(refCode))
            return 0;
        // Get user's department and division using userId
        var (division, department) = await GetUserDivisionAndDeptByUserId(user);

        // Start a new transaction
        await using var transaction = await dc.Database.BeginTransactionAsync();
        try
        {
            int newMemoId;
            if (memoId == -1)
            {
                var q = new Memo
                {
                    MemoCode = "",
                    MemoTitle = memoDto.MemoTitle,
                    MemoStatus = memoStatus,
                    MemoCreatedDate = DateTime.Now,
                    MemoCreatedBy = user,
                    MemoModifiedDate = DateTime.Now,
                    MemoModifiedBy = user,
                    MemoIsActive = true,
                    MemoIsDel = false,
                    MemoTemplateId = memoDto.MemoTemplateId,
                    MemoTypeId = memoDto.MemoTypeId,
                    MemoStatusId = (byte?)(memoStatus == "DRAFT" ? 1 : 2),
                    MemoDepartment = department, // Add department
                    MemoDivision = division // Add division
                };
                q.MemoCode = refCode;
                q.MemoIsActive = true;
                q.FormTypeId = 1;
                dc.Memos.Add(q);
                await dc.SaveChangesAsync();
                memoId = q.MemoId;
                newMemoId = q.MemoId;
            }
            else
            {
                var q = dc.Memos.SingleOrDefault(x => x.MemoId == memoId);
                if (q != null)
                {
                    q.MemoTitle = memoDto.MemoTitle;
                    q.MemoTypeId = memoDto.MemoTypeId;
                    q.MemoStatus = memoStatus;
                    q.MemoModifiedDate = DateTime.Now;
                    q.MemoModifiedBy = user;
                    q.MemoStatusId = (byte?)(memoStatus == "DRAFT" ? 1 : 2);
                    q.MemoDepartment = department; // Update department
                    q.MemoDivision = division; // Update division
                }

                newMemoId = memoId;
                await dc.SaveChangesAsync();
            }

            await SaveMemoSections(memoId, memoSections, user, dc);
            await SaveMemoAttachments(memoId, memoAttachments, user, dc);

            if (!isLogAtObjectState) await SaveMemoApprovers(memoId, memoApprovers, user, memoStatus, dc);

            if (memoStatus != "DRAFT")
            {
                // insert first record in memo approval log
                var qLog = new MemoApprovalLog
                {
                    MemoId = memoId,
                    ApprovalActionsId = 1,
                    FromApproverId = GetMemoInitiator(memoId)
                };
                qLog.ToApproverId = GetSecondApprover(memoId, qLog.FromApproverId);
                if (qLog.ToApproverId == 0)
                {
                    // If GetSecondApprover returns 0, it indicates an issue, so rollback.
                    await transaction.RollbackAsync();
                    return 0;
                }

                qLog.ActionDate = DateTime.Now;
                qLog.IsForwarded = true;
                dc.MemoApprovalLogs.Add(qLog);
                await dc.SaveChangesAsync();
            }

            // Commit the transaction if all operations are successful
            await transaction.CommitAsync();
            return newMemoId;
        }
        catch (Exception ex)
        {
            // Rollback the transaction if any error occurs
            await transaction.RollbackAsync();
            // You might want to log the exception here
            Console.WriteLine($"Error saving memo: {ex.Message}");
            return 0; // Or throw the exception, depending on your error handling strategy
        }
    }

    public async Task<int> SaveMemo(int memoId, MemoDto memoDto, List<MemoSectionDto> memoSections,
        List<MemoAttachmentDto> memoAttachments, List<MemoApproverDto> memoApprovers, string user, string memoStatus,
        bool isLogAtObjectState)
    {
        var dc = await contextFactory.CreateDbContextAsync();
        var userCode = GetCodeOnly(user);
        var deptCode = GetDepartmentNickByEmployeeNo(dc, userCode);
        if (string.IsNullOrEmpty(deptCode))
            return 0;
        var templateCode = dc.MemoTemplates.FirstOrDefault(c => c.MemoTemplateId == memoDto.MemoTemplateId)!
            .MemoTemplateCode ?? "";
        var refCode = Get_RefCode(dc, user, templateCode, deptCode);
        if (string.IsNullOrEmpty(refCode))
            return 0;
        // Get user's department and division using userId
        var (division, department) = await GetUserDivisionAndDeptByUserId(user);
        int newMemoId;
        if (memoId == -1)
        {
            var q = new Memo
            {
                MemoCode = "",
                MemoTitle = memoDto.MemoTitle,
                MemoStatus = memoStatus,
                MemoCreatedDate = DateTime.Now,
                MemoCreatedBy = user,
                MemoModifiedDate = DateTime.Now,
                MemoModifiedBy = user,
                MemoIsActive = true,
                MemoIsDel = false,
                MemoTemplateId = memoDto.MemoTemplateId,
                MemoTypeId = memoDto.MemoTypeId,
                MemoStatusId = (byte?)(memoStatus == "DRAFT" ? 1 : 2),
                MemoDepartment = department, // Add department
                MemoDivision = division // Add division
            };
            q.MemoCode = refCode;
            q.MemoIsActive = true;
            q.FormTypeId = 1;
            dc.Memos.Add(q);
            await dc.SaveChangesAsync();
            memoId = q.MemoId;
            newMemoId = q.MemoId;
        }
        else
        {
            var q = dc.Memos.SingleOrDefault(x => x.MemoId == memoId);
            if (q != null)
            {
                q.MemoTitle = memoDto.MemoTitle;
                q.MemoTypeId = memoDto.MemoTypeId;
                q.MemoStatus = memoStatus;
                q.MemoModifiedDate = DateTime.Now;
                q.MemoModifiedBy = user;
                q.MemoStatusId = (byte?)(memoStatus == "DRAFT" ? 1 : 2);
                q.MemoDepartment = department; // Update department
                q.MemoDivision = division; // Update division
            }

            newMemoId = memoId;
            await dc.SaveChangesAsync();
        }

        await SaveMemoSections(memoId, memoSections, user, dc);
        await SaveMemoAttachments(memoId, memoAttachments, user, dc);
        if (!isLogAtObjectState) await SaveMemoApprovers(memoId, memoApprovers, user, memoStatus, dc);
        if (memoStatus != "DRAFT")
        {
            // insert first record in memo approval log
            var qLog = new MemoApprovalLog
            {
                MemoId = memoId,
                ApprovalActionsId = 1,
                FromApproverId = GetMemoInitiator(memoId)
            };
            qLog.ToApproverId = GetSecondApprover(memoId, qLog.FromApproverId);
            if (qLog.ToApproverId == 0)
                return 0;
            qLog.ActionDate = DateTime.Now;
            qLog.IsForwarded = true;
            dc.MemoApprovalLogs.Add(qLog);
            await dc.SaveChangesAsync();
        }

        //await transaction.CommitAsync();
        return newMemoId;
    }

    private static async Task SaveMemoApprovers(int memoId, List<MemoApproverDto> memoApprovers, string user,
        string memoStatus,
        ApplicationDbContext dc)
    {
        var tempMemoApprover = dc.MemoApprovers.Where(x => x.MemoId == memoId).ToList();
        foreach (var item in tempMemoApprover) dc.MemoApprovers.Remove(item);
        await dc.SaveChangesAsync();

        // Optimization: Remove duplicate approvers by UserId if publishing
        List<MemoApproverDto> filteredApprovers;
        if (memoStatus.ToUpper() == "PUBLISHED")
        {
            //var seen = new HashSet<string>();
            var seen = new List<string>();
            filteredApprovers = new List<MemoApproverDto>();
            foreach (var approver in memoApprovers.OrderBy(c => c.SortOrder))
                //if (string.IsNullOrEmpty(approver.UserId) || seen.Add(approver.UserId))
                // {
                //    filteredApprovers.Add(approver);
                // }
                if (!seen.Contains(approver.UserId))
                {
                    seen.Add(approver.UserId);
                    filteredApprovers.Add(approver);
                }
        }
        else
        {
            filteredApprovers = memoApprovers.OrderBy(c => c.SortOrder).ToList();
        }

        var idx = 0;

        // if no initiator exist in filteredApprovers then add it.
        var init = filteredApprovers.Any(c => c.UserRoleType == "Initiator");
        

        foreach (var item in filteredApprovers)
        {
            //var userId = item.UserId;
            //if (!string.IsNullOrEmpty(userId) && userId.Length >= 5 && userId.ToLower() != "khi-soft-056\\jawaid")
            //{
            //    var lastFive = userId.Substring(userId.Length - 5);
            //    item.UserId = "psmcl\\pkb0" + lastFive;
            //}



            if (item.UserId!=null && item.UserId.Trim().Length == 5)
            {
                item.UserId = "psmcl\\pkb0" + item.UserId.Trim();
            }


            var qApprover = new MemoApprover();
            qApprover.MemoId = memoId;
            qApprover.MemoApproverTitle = item.Title;
            qApprover.MemoApproverUserId = item.UserId;
            qApprover.MemoApproverUserName = item.User; //MemoApproverUserDetail = item.MemoApproverDesignation,
            qApprover.Designation = item.Designation;
            qApprover.Department = item.Department;
            qApprover.MemoAllowType = item.AllowType;
            qApprover.MemoApproverRoleId = item.MemoApproverRoleId;
            qApprover.MemoApproverSortOrder = idx;
            qApprover.MemoApproverCode = $"{memoId:000}";
            qApprover.MemoApproverIsActive = true;
            qApprover.MemoApproverIsDel = false;
            qApprover.MemoApproverCreatedBy = user;
            qApprover.MemoApproverCreatedDate = DateTime.Now;
            qApprover.MemoApproverModifiedBy = user;
            qApprover.MemoApproverModifiedDate = DateTime.Now;
            qApprover.MemoApproverUserEmail = item.Email;
            dc.MemoApprovers.Add(qApprover);
            idx++;
        }

        await dc.SaveChangesAsync();
    }

    private static async Task SaveMemoAttachments(int memoId, List<MemoAttachmentDto> memoAttachments, string user,
        ApplicationDbContext dc)
    {
        var tempMemoFile = dc.MemoAttachments.Where(x => x.MemoId == memoId).ToList();
        foreach (var item in tempMemoFile)
            dc.MemoAttachments.Remove(item);
        await dc.SaveChangesAsync();
        foreach (var item in memoAttachments)
        {
            var qAttachment = new MemoAttachment
            {
                MemoAttachmentCode = $"{memoId:000}",
                MemoAttachmentTitle = item.Name,
                MemoAttachmentDocName = item.Name,
                MemoAttachmentDocType = item.Type,
                MemoAttachmentDocSizeMb = Convert.ToInt32(item.Size),
                MemoAttachmentFilePath = item.Path,
                MemoAttachmentIsActive = true,
                MemoAttachmentIsDel = false,
                MemoAttachmentCreatedBy = user,
                MemoAttachmentCreatedDate = DateTime.Now,
                MemoAttachmentModifiedBy = user,
                MemoAttachmentModifiedDate = DateTime.Now,
                MemoAttachmentDescription = item.Description,
                AttachmentTypeId = item.AttachmentTypeId,
                MemoId = memoId
            };
            qAttachment.MemoAttachmentFilePath = qAttachment.MemoAttachmentFilePath.Replace("\\wwwroot\\temp\\",
                "\\wwwroot\\Memos\\Attachments\\" + memoId + "\\");
            dc.MemoAttachments.Add(qAttachment);
            await dc.SaveChangesAsync();
        }
    }

    private static async Task SaveMemoSections(int memoId, List<MemoSectionDto> memoSections, string user,
        ApplicationDbContext dc)
    {
        var tempMemoSection = dc.MemoSections.Where(x => x.MemoId == memoId).ToList();
        foreach (var item in tempMemoSection)
            item.MemoSectionIsDel = true;
        await dc.SaveChangesAsync();
        foreach (var item in memoSections)
        {
            // Set MemoId for encryption/decryption
            item.MemoId = memoId;

            var qSection = dc.MemoSections.FirstOrDefault(s => s.MemoSectionId == item.Id);
            if (qSection != null)
            {
                qSection.MemoId = memoId;
                qSection.MemoSectionCode = $"{memoId:000}";
                qSection.MemoSectionTitle = item.TemplateSectionTitle;

                // Store content using encryption if enabled
                qSection.MemoSectionContentHtml = EncryptionService.EncryptAndCompress(item.ContentHtml ?? "", item.MemoId.Value); //item.GetContentForStorage();
                    qSection.MemoSectionContentText = null; //item.GetTextContentForStorage();
                qSection.MemoIsEncrypted = true;

                qSection.MemoSectionSortOrder = item.SectionSortOrder;
                qSection.MemoSectionIsActive = item.MemoIsActive;
                qSection.MemoSectionIsDel = false;
                qSection.MemoSectionModifiedBy = user;
                qSection.MemoSectionModifiedDate = DateTime.Now;
                qSection.MemoTemplateSectionId = item.MemoTemplateSectionId;
                qSection.MemoSectionIgnored = item.IgnoreSection;
                await dc.SaveChangesAsync();
            }
            else
            {
                qSection = new MemoSection
                {
                    MemoSectionId = item.Id,
                    MemoId = memoId,
                    MemoSectionCode = $"{memoId:000}",
                    MemoSectionTitle = item.TemplateSectionTitle,

                    // Store content using encryption if enabled
                    MemoSectionContentHtml = EncryptionService.EncryptAndCompress(item.ContentHtml ?? "", item.MemoId.Value), //item.GetContentForStorage();
                    //MemoSectionContentText = item.GetTextContentForStorage(),
                    MemoIsEncrypted = item.MemoIsEncrypted,

                    MemoSectionSortOrder = item.SectionSortOrder,
                    MemoSectionIsActive = item.MemoIsActive,
                    MemoSectionIsDel = false,
                    MemoSectionCreatedBy = user,
                    MemoSectionCreatedDate = DateTime.Now,
                    MemoSectionModifiedBy = user,
                    MemoSectionModifiedDate = DateTime.Now,
                    MemoTemplateSectionId = item.MemoTemplateSectionId
                };
                qSection.MemoSectionIsActive = true;
                qSection.MemoSectionIgnored = item.IgnoreSection;
                dc.MemoSections.Add(qSection);
                await dc.SaveChangesAsync();
            }
        }
    }

    private string Get_RefCode(ApplicationDbContext dc, string user, string templateCode, string deptCode)
    {
        var code = GetCodeOnly(user);
        var con = dc.Database.GetDbConnection();
        var res = con.Query<string>("select dbo.Get_RefCode('" + code + "','" + templateCode + "', '" + deptCode + "')",
            commandType: CommandType.Text).FirstOrDefault();
        return res ?? "";
    }

    private string GetDepartmentNickByEmployeeNo(ApplicationDbContext dc, string userCode)
    {
        userCode = GetCodeOnly(userCode);
        var con = dc.Database.GetDbConnection();
        var res = con.Query<string>("select dbo.GetDepartmentNickByEmployeeNo('" + userCode + "')",
            commandType: CommandType.Text).FirstOrDefault();
        return res ?? "";
    }

    private int? GetSecondApprover(int memoId, int fromApproverId)
    {
        var dc = contextFactory.CreateDbContext();
        var q = (from a in dc.MemoApprovers
            where a.MemoId == memoId &&
                  a.MemoApproverId != fromApproverId
            orderby a.MemoApproverSortOrder
            select a.MemoApproverId).FirstOrDefault();
        return q;
    }

    private int GetMemoInitiator(int memoId)
    {
        var dc = contextFactory.CreateDbContext();
        var q = (from a in dc.MemoApprovers
            where a.MemoId == memoId &&
                  a.MemoApproverSortOrder == 0
            select a.MemoApproverId).First();
        return q;
    }

    private Task<User> GetUserDetail(string userId)
    {
        var dc = contextFactory.CreateDbContext();
        var q = dc.Users.FirstOrDefault(m => m.UserId == userId);
        return Task.FromResult(q!);
    }

    public async Task<List<MemoTypeDto>> GetMemoTypes()
    {
        var dc = await contextFactory.CreateDbContextAsync();
        var q = (from a in dc.MemoTypes
            where a.MemoTypeIsActive == true && a.MemoTypeIsDel == false
            orderby a.MemoTypeName
            select new MemoTypeDto
            {
                Id = a.MemoTypeId,
                Title = a.MemoTypeName + " - " + (string.IsNullOrEmpty(a.MemoTypeCode) ? "" : a.MemoTypeCode),
                Code = a.MemoTypeCode
            }).ToList();
        return await Task.FromResult(q);
    }

    private string? GetEmployeeUserIdByCode(string code)
    {
        var dc = contextFactory.CreateDbContext();
        var q = (from a in dc.Users
            where a.EmployeeCode == code
            select a.UserId).FirstOrDefault();
        return q;
    }

    public async Task<List<MemoApproverDto>> GetMemoApprovers(int memoId, int memoTemplateId, string userId)
    {
        var allEmployees = await corporateService.GetAllEmployeeData();

        var dc = await contextFactory.CreateDbContextAsync();
        var memoApproversList = new List<MemoApproverDto>();
        var qExists = dc.MemoApprovers.Any(x => x.MemoId == memoId);
        if (qExists)
        {
            memoApproversList = (from a in dc.MemoApprovers
                where a.MemoId == memoId
                orderby a.MemoApproverSortOrder
                select new MemoApproverDto
                {
                    UserId = a.MemoApproverUserId,
                    User = a.MemoApproverUserName,
                    Role = a.MemoApproverRole.MemoApproverRoleTitle,
                    Title = a.MemoApproverRole.MemoApproverRoleTitle,
                    Email = a.MemoApproverUserEmail,
                    Status = "",
                    SortOrder = a.MemoApproverSortOrder,
                    AllowType = a.MemoAllowType,
                    //userRoleType = b.MemoApproverRoleType,
                    UserRoleType = a.MemoApproverRole.MemoApproverRoleType,
                    Id = a.Id,
                    MemoApproverId = a.MemoApproverId,
                    //MemoApproverDesignation = a.MemoApproverUserDetail, // designation
                    Designation = a.Designation,
                    Department = a.Department,
                    MemoApproverRoleId = a.MemoApproverRoleId
                }).ToList();

            foreach (var ma in memoApproversList)
                if (ma.UserId != null)
                {
                    //var emp = (from a in allEmployees
                    //    where a.EmpCode is { Length: >= 5 } &&
                    //          ma.UserId.Contains(a.EmpCode.Substring(a.EmpCode.Length - 5))
                    //    select a).FirstOrDefault();
                    //var emp = (from a in allEmployees
                    //    where a.EmployeeNo != null && (a.EmployeeNo ?? "").Trim().Length >= 5 &&
                    //          ma.UserId.Contains(a.EmployeeNo.Substring((a.EmployeeNo ?? "").Trim().Length - 5))
                    //    select a).FirstOrDefault();

                    var emp = (from a in allEmployees
                               where ma.UserId.EndsWith(a.CodeOnly)
                               select a).FirstOrDefault();

                    if (emp != null)
                    {
                        ma.Department = emp.DepartmentName;
                        ma.Designation = emp.JobTitle;
                        ma.Email = emp.EmployeeOfficialEmail.ToLower();
                    }
                }

            return await Task.FromResult(memoApproversList);
        }
        else
        {
            var qTemplateHead =
                (from a in dc.MemoTemplateApprovers
                 orderby a.MemoTemplateApproverSortOrder
                 where a.MemoTemplateId == memoTemplateId &&
                       a.MemoTemplateApproverIsActive &&
                       a.MemoTemplateApproverIsDel == false &&
                       a.MemoTemplateApproverAllowType != "Must Not Required"
                 select new
                 {
                     a.MemoTemplateApproverTitle,
                     a.MemoTemplateApproverAllowType,
                     a.MemoTemplateApproverSortOrder,
                     a.MemoApproverRoleId,
                     a.MemoApproverRole.MemoApproverRoleUserName,
                     a.MemoTemplateApproverUserEmail,
                     a.MemoTemplateApproverUserId,
                     a.MemoTemplateApproverCode,
                     a.MemoApproverRole.MemoApproverRoleUserId,
                     a.MemoApproverRole.MemoApproverRoleDesc,
                     a.MemoApproverRole.MemoApproverRoleUserCode,
                     a.MemoApproverRole.MemoApproverRoleType,
                     a.MemoApproverRole.MemoApproverRoleUserEmail,
                     a.MemoApproverRole.MemoApproverRoleUserDesg,
                     a.MemoApproverRole.MemoApproverRoleTitle
                 }).ToList();
            var templateEmp = await GetEmployeeByHead(userId);
            foreach (var item in qTemplateHead)
                if (item.MemoApproverRoleType == "Fixed")
                {
                    memoApproversList.Add(new MemoApproverDto
                    {
                        UserId = (item.MemoApproverRoleUserId ?? "").Trim(),
                        User = item.MemoApproverRoleUserName,
                        Role = item.MemoApproverRoleUserDesg,
                        Title = item.MemoTemplateApproverTitle,
                        Email = item.MemoApproverRoleUserEmail,
                        Status = "",
                        SortOrder = item.MemoTemplateApproverSortOrder,
                        AllowType = item.MemoTemplateApproverAllowType,
                        UserRoleType = item.MemoApproverRoleType,
                        MemoApproverRoleId = item.MemoApproverRoleId
                    });
                }
                else if (item.MemoApproverRoleType is "User Defined")
                {
                    memoApproversList.Add(new MemoApproverDto
                    {
                        User = item.MemoTemplateApproverTitle,
                        Role = "",
                        Title = item.MemoTemplateApproverTitle,
                        Email = "",
                        Status = "",
                        SortOrder = item.MemoTemplateApproverSortOrder,
                        AllowType = item.MemoTemplateApproverAllowType,
                        UserRoleType = item.MemoApproverRoleType,
                        MemoApproverRoleId = item.MemoApproverRoleId
                    });
                }
                else
                {
                    var emp = templateEmp.FirstOrDefault(x => x.RoleType == item.MemoTemplateApproverTitle);
                    if (emp != null)
                        memoApproversList.Add(new MemoApproverDto
                        {
                            UserId = GetEmployeeUserIdByCode(emp.EmpNo),
                            User = emp.EmpName,
                            //MemoApproverDesignation = emp.DesignationName,
                            Designation = emp.DesignationName,
                            //Department = emp.DepartmentName,
                            Title = item.MemoTemplateApproverTitle,
                            Email = emp.Email,
                            Status = "",
                            SortOrder = item.MemoTemplateApproverSortOrder,
                            AllowType = item.MemoTemplateApproverAllowType,
                            UserRoleType = item.MemoApproverRoleType,
                            MemoApproverRoleId = item.MemoApproverRoleId
                        });
                }

            if ((memoId == -1 && memoTemplateId != 0) || memoApproversList.All(c => c.Title != "Initiator"))
            {
                var u = await GetUserDetail(userId);
                memoApproversList.Insert(0, new MemoApproverDto
                {
                    UserId = u.UserId,
                    User = u.Name,
                    Role = "",
                    Title = "Initiator",
                    Email = u.Email,
                    Status = "",
                    SortOrder = 0,
                    AllowType = "Must Required",
                    UserRoleType = "Initiator",
                    MemoApproverRoleId = 8
                });
                // if (u != null)
            }

            foreach (var ma in memoApproversList)
                if (ma.UserId != null)
                {
                    //var emp = (from a in allEmployees
                    //    where a.EmployeeNo != null && (a.EmployeeNo??"").Trim().Length >= 5 &&
                    //          ma.UserId.Contains(a.EmployeeNo.Substring((a.EmployeeNo??"").Trim().Length - 5))
                    //    select a).FirstOrDefault();
                    var emp = (from a in allEmployees
                               where ma.UserId.EndsWith(a.CodeOnly)
                               select a).FirstOrDefault();

                    if (emp != null)
                    {
                        ma.Department = emp.DepartmentName;
                        ma.Designation = emp.JobTitle;
                        ma.Email = emp.EmployeeOfficialEmail.ToLower();
                    }
                }

            return await Task.FromResult(memoApproversList);
        }
    }

    private async Task<List<EmployeeHeadDto>> GetEmployeeByHead(string code)
    {
        //code = GetCodeOnly(code);
        //last 5 digit of code
        code = GetCodeOnly(code);
        //code = EmpNoUpdate(code);
        code = HttpUtility.UrlEncode(code);
        var apiBaseUrl = configuration.GetSection("apibaseurl").Value;
        var url = $"{apiBaseUrl}DynamicRoles/{code}";
        var response = await http.GetFromJsonAsync<List<EmployeeHeadDto>>(url);
        return response ?? new List<EmployeeHeadDto>();
    }

    public string GetCodeOnly(string code)
    {
        if (string.IsNullOrEmpty(code)) return "";
        if (code == @"KHI-SOFT-056\Jawaid" || code == @"NABIL-PC\Administrator")
            return "00420";
        if (code.Length < 5) return code;
        return code.Substring(code.Length - 5);
    }

    public Task<string> SaveMemoApprover(MemoApproverDto app, string userId)
    {
        var dc = contextFactory.CreateDbContext();
        var q = (from a in dc.MemoApprovers
            where a.Id == app.Id
            select a).FirstOrDefault();
        if (q != null)
        {
            q.MemoApproverUserId = app.UserId;
            q.MemoApproverUserName = app.User;
            q.MemoApproverUserEmail = app.Email;
            //q.MemoApproverUserDetail = app.MemoApproverDesignation;
            q.Designation = app.Designation;
            q.Department = app.Department;
            dc.SaveChanges();
            return Task.FromResult("OK");
        }

        return Task.FromResult("Record not found");
    }

    public Task<string> GetDynamicUrl(string code)
    {
        //
        code = EmpNoUpdate(code);
        var apiBaseUrl = configuration.GetSection("apibaseurl").Value;
        var url = $"{apiBaseUrl}DynamicRoles/{code}";
        return Task.FromResult(url);
    }

    private string EmpNoUpdate(string empNo)
    {
        // Handle special cases
        if (empNo == @"KHI-SOFT-056\Jawaid" || empNo == @"NABIL-PC\Administrator")
            return "123456";
        // Clean up input
        empNo = empNo.ToLower().Replace("psmcl\\pkb0", "");
        var numericEmpNo = long.Parse(empNo);
        // Handle numbers 1-1000 with padding
        if (numericEmpNo is >= 1 and <= 1000) return "00100" + empNo.PadLeft(5, '0');
        // Map ranges to prefixes
        var rangeMap = new Dictionary<(long Start, long End), string>
        {
            { (20001, 21000), "001" },
            { (21001, 29999), "005" },
            { (30001, 39999), "001" },
            { (50001, 69999), "003" },
            { (70001, 79999), "003" },
            { (80001, 89999), "004" },
            { (95001, 99999), "006" },
            { (1001, 19999), "008" } // Combined the last two ranges as they use same prefix
        };
        foreach (var range in rangeMap)
            if (numericEmpNo >= range.Key.Start && numericEmpNo <= range.Key.End)
                return range.Value + empNo;

        return empNo; // Return original if no range matches
    }

    public Task<string> GetDeptShort(string userId)
    {
        var dc = contextFactory.CreateDbContext();
        var msg = GetDepartmentNickByEmployeeNo(dc, userId);
        return Task.FromResult(msg);
    }

    public Task<TemplateDto?> GetMemoTemplate(int memoId)
    {
        var dc = contextFactory.CreateDbContext();
        var q = (from a in dc.Memos
            where a.MemoId == memoId
            select new TemplateDto
            {
                TemplateId = a.MemoTemplateId ?? 0,
                MemoTypeId = a.MemoTypeId,
                TemplateTitle = a.MemoTemplate.MemoTemplateTitle,
                MemoTemplateCode = a.MemoTemplate.MemoTemplateCode,
                MemoTemplatePrefixCode = a.MemoTemplate.MemoTemplatePrefixCode,
                MemoTemplateAttachmentAllowed = a.MemoTemplate.MemoTemplateAttachmentAllowed == true,
                MemoTemplateAttachmentPerFileSizeMbAllowed =
                    a.MemoTemplate.MemoTemplateAttachmentPerFileSizeMballowed ?? 0,
                MemoTemplateAttachmentFileCountAllowed =
                    (int)(a.MemoTemplate.MemoTemplateAttachmentFileCountAllowed ?? 0)
            }).FirstOrDefault();
        return Task.FromResult(q);
    }

    public Task<bool> IsMemoAtObjectState(int memoId)
    {
        var dc = contextFactory.CreateDbContext();
        var logs = dc.MemoApprovalLogs
            .OrderByDescending(c => c.ActionDate)
            .FirstOrDefault(c => c.MemoId == memoId &&
                                 c.ReplyLogId == null);
        if (logs == null)
            return Task.FromResult(false);
        if (logs.ApprovalActionsId == 4)
            return Task.FromResult(true);
        return Task.FromResult(false);
    }

    public Task<MemoDto?> GetMemoPreview(int memoId)
    {
        var dc = contextFactory.CreateDbContext();
        var q = (from a in dc.Memos
            where a.MemoId == memoId &&
                  a.MemoIsDel == false &&
                  a.MemoIsActive
            select new MemoDto
            {
                LastAction = a.LastAction,
                MemoStatus = a.MemoStatus,
                MemoId = memoId,
                MemoCode = a.MemoCode,
                MemoCreatedBy = a.MemoCreatedBy,
                MemoCreatedDate = a.MemoCreatedDate,
                MemoStatusId = a.MemoStatusId,
                MemoTemplateId = a.MemoTemplateId,
                MemoTitle = a.MemoTitle,
                MemoTypeId = a.MemoTemplate.MemoTypeId,
                MemoTypeStr = a.MemoTemplate.MemoType.MemoTypeName,
                Department = a.MemoDepartment ?? "",
                Division = a.MemoDivision ?? "",
                InitiatedBy = (from u in dc.Users where u.UserId == a.MemoCreatedBy select u.Name).FirstOrDefault() ??
                              "",
                MemoSections = (from s in a.MemoSections
                        orderby s.MemoSectionSortOrder
                        where s.MemoSectionIsActive &&
                              s.MemoSectionIsDel == false &&
                              s.MemoSectionIgnored == false
                        select new MemoSection
                        {
                            MemoSectionContentHtml = s.MemoIsEncrypted && !string.IsNullOrEmpty(s.MemoSectionContentHtml)
                                ? EncryptionService.DecryptAndDecompress(s.MemoSectionContentHtml, memoId)
                                : s.MemoSectionContentHtml,
                            MemoSectionTitle = s.MemoSectionTitle,
                            MemoSectionIgnored = s.MemoSectionIgnored
                        }
                    ).ToList()
            }).FirstOrDefault();
        return Task.FromResult(q);
    }

    public async Task<(string, string)> GetUserDivisionAndDeptByMemoId(int memoId)
    {
        var dc = await contextFactory.CreateDbContextAsync();
        var q = (from a in dc.Memos
            where a.MemoId == memoId
            select a).FirstOrDefault();

        if (q == null)
            return ("", "");

        var userId = q.MemoCreatedBy;
        var userIdLast5 = GetCodeOnly(userId);

        try
        {
            var apiBaseUrl = configuration.GetSection("apibaseurl").Value;
            var departmentEndpoint = configuration.GetSection("departmentEndpoint").Value ?? "Department)";
            var url = $"{apiBaseUrl}{departmentEndpoint}/{userIdLast5}";

            var response = await http.GetFromJsonAsync<List<EmployeeDepartmentDto>>(url);

            if (response != null && response.Any()) return (response[0].EmpDiv, response[0].EmpDep);

            return ("", "");
        }
        catch (Exception)
        {
            return ("", "");
        }
    }

    public async Task<(string, string)> GetUserDivisionAndDeptByUserId(string userId)
    {
        var userIdLast5 = GetCodeOnly(userId);

        try
        {
            var apiBaseUrl = configuration.GetSection("apibaseurl").Value;
            var departmentEndpoint = configuration.GetSection("departmentEndpoint").Value ?? "Department)";
            var url = $"{apiBaseUrl}{departmentEndpoint}/{userIdLast5}";

            var response = await http.GetFromJsonAsync<List<EmployeeDepartmentDto>>(url);

            if (response != null && response.Any()) return (response[0].EmpDiv, response[0].EmpDep);

            return ("", "");
        }
        catch (Exception)
        {
            return ("", "");
        }
    }

    public async Task<int> DuplicateMemo(int sourceMemoId, string userId)
    {
        var dc = await contextFactory.CreateDbContextAsync();

        // Get the source memo
        var sourceMemo = await dc.Memos
            .Include(m => m.MemoSections)
            .FirstOrDefaultAsync(m => m.MemoId == sourceMemoId);
        if (sourceMemo == null) throw new ArgumentException("Source memo not found");
        // Get user's department and division
        var (division, department) = await GetUserDivisionAndDeptByUserId(userId);

        // Get template code for new reference number
        var templateCode = dc.MemoTemplates.FirstOrDefault(c => c.MemoTemplateId == sourceMemo.MemoTemplateId)
            ?.MemoTemplateCode ?? "";
        var userCode = GetCodeOnly(userId);
        var deptCode = GetDepartmentNickByEmployeeNo(dc, userCode);

        if (string.IsNullOrEmpty(deptCode))
            return 0;
        var newRefCode = Get_RefCode(dc, userId, templateCode, deptCode);
        if (string.IsNullOrEmpty(newRefCode))
            return 0;
        // Create new memo
        var newMemo = new Memo
        {
            MemoCode = newRefCode,
            MemoTitle = $"Copy of - {sourceMemo.MemoTitle}",
            MemoStatus = "DRAFT",
            MemoCreatedDate = DateTime.Now,
            MemoCreatedBy = userId,
            MemoModifiedDate = DateTime.Now,
            MemoModifiedBy = userId,
            MemoIsActive = true,
            MemoIsDel = false,
            MemoTemplateId = sourceMemo.MemoTemplateId,
            MemoTypeId = sourceMemo.MemoTypeId,
            MemoStatusId = 1, // Draft status
            MemoDepartment = department,
            MemoDivision = division,
            FormTypeId = sourceMemo.FormTypeId
        };
        dc.Memos.Add(newMemo);
        await dc.SaveChangesAsync();
        // Copy memo sections
        foreach (var sourceSection in sourceMemo.MemoSections.Where(s => !s.MemoSectionIsDel))
        {
            // Handle encrypted content for duplicated memo
            string contentHtml = sourceSection.MemoSectionContentHtml;
            if (sourceSection.MemoIsEncrypted && !string.IsNullOrEmpty(sourceSection.MemoSectionContentHtml))
            {
                try
                {
                    // Decrypt the source content
                    var decryptedContent = EncryptionService.DecryptAndDecompress(sourceSection.MemoSectionContentHtml, sourceMemoId);
                    // Re-encrypt with new memo ID
                    contentHtml = EncryptionService.EncryptAndCompress(decryptedContent, newMemo.MemoId);
                }
                catch
                {
                    // If decryption fails, copy as-is (backward compatibility)
                    contentHtml = sourceSection.MemoSectionContentHtml;
                }
            }

            var newSection = new MemoSection
            {
                MemoId = newMemo.MemoId,
                MemoSectionCode = $"{newMemo.MemoId:000}",
                MemoSectionTitle = sourceSection.MemoSectionTitle,
                MemoSectionContentHtml = contentHtml,
                MemoSectionContentText = sourceSection.MemoSectionContentText,
                MemoSectionSortOrder = sourceSection.MemoSectionSortOrder,
                MemoSectionIsActive = true,
                MemoSectionIsDel = false,
                MemoSectionCreatedBy = userId,
                MemoSectionCreatedDate = DateTime.Now,
                MemoSectionModifiedBy = userId,
                MemoSectionModifiedDate = DateTime.Now,
                MemoTemplateSectionId = sourceSection.MemoTemplateSectionId,
                MemoSectionIgnored = sourceSection.MemoSectionIgnored,
                MemoIsEncrypted = true // New memos should be encrypted
            };
            dc.MemoSections.Add(newSection);
        }

        await dc.SaveChangesAsync();
        return newMemo.MemoId;
    }

    public Task<string> DeleteMemo(int memoId, string userId)
    {
        var dc = contextFactory.CreateDbContext();
        var memo = dc.Memos.FirstOrDefault(c => c.MemoId == memoId && c.MemoIsActive && c.MemoIsDel == false);
        if (memo == null)
            return Task.FromResult("Memo Id not found");
        memo.MemoIsDel = true;
        memo.MemoModifiedBy = userId;
        memo.MemoModifiedDate = DateTime.Now;
        dc.SaveChanges();
        return Task.FromResult("OK");
    }

    public Task<UserDTO?> GetUserInfo(string userId)
    {
        var dc = contextFactory.CreateDbContext();
        var q = (from a in dc.Users
            where a.UserId == userId
            select new UserDTO
            {
                Code = a.Code,
                UserId = a.UserId,
                Name = a.Name,
                Email = a.Email,
                Department = ""
            }).FirstOrDefault();
        return Task.FromResult(q);
    }

    public Task<bool> IsMyMemo(int memoId, string userId)
    {
        var dc = contextFactory.CreateDbContext();
        var isMyMemo = (from a in dc.MemoApprovalLogs
            where a.MemoId == memoId &&
                  (a.FromApprover.MemoApproverUserId == userId ||
                   a.ToApprover.MemoApproverUserId == userId)
            orderby a.ActionDate descending
            select a).Any();
        return Task.FromResult(isMyMemo);
    }
}