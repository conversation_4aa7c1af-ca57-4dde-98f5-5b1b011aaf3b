@using PSSmartMemo.DTO
@using Syncfusion.Blazor.DropDowns
@using Syncfusion.Blazor.Inputs
@using Syncfusion.Blazor.Buttons
@using MudBlazor
@inject AdminDataService AdminDataService
@inject CorporateService CorporateService

<div class="approver-workflow-step">
    <div class="step-header">
        <h2 class="step-title">
            <MudIcon Icon="@Icons.Material.Filled.Approval" Class="step-icon" />
            Approver Workflow Configuration
        </h2>
        <p class="step-description">
            Configure the approval workflow for your memo. Add, remove, or reorder approvers as needed.
        </p>
    </div>

    <div class="step-content">
        <div class="workflow-section">
            <div class="section-header">
                <h3>Current Approval Workflow</h3>
                <MudButton Variant="Variant.Filled" 
                          Color="Color.Primary" 
                          StartIcon="@Icons.Material.Filled.Add"
                          OnClick="@OpenAddApproverDialog"
                          Size="Size.Small">
                    Add Approver
                </MudButton>
            </div>

            @if (MemoApprovers != null && MemoApprovers.Any())
            {
                <div class="approvers-list">
                    @foreach (var approver in MemoApprovers.OrderBy(a => a.SortOrder))
                    {
                        <div class="approver-item">
                            <div class="approver-order">
                                <span class="order-number">@approver.SortOrder</span>
                            </div>
                            
                            <div class="approver-info">
                                <div class="approver-name">
                                    @if (!string.IsNullOrEmpty(approver.User))
                                    {
                                        <strong>@approver.User</strong>
                                    }
                                    else
                                    {
                                        <em class="text-muted">User not selected</em>
                                    }
                                </div>
                                <div class="approver-details">
                                    <span class="role">@approver.Role</span>
                                    @if (!string.IsNullOrEmpty(approver.Email))
                                    {
                                        <span class="email">• @approver.Email</span>
                                    }
                                    @if (!string.IsNullOrEmpty(approver.Department))
                                    {
                                        <span class="department">• @approver.Department</span>
                                    }
                                </div>
                            </div>

                            <div class="approver-actions">
                                <MudIconButton Icon="@Icons.Material.Filled.Edit" 
                                              Size="Size.Small" 
                                              Color="Color.Primary"
                                              OnClick="@(() => EditApprover(approver))" />
                                <MudIconButton Icon="@Icons.Material.Filled.ArrowUpward" 
                                              Size="Size.Small" 
                                              Color="Color.Default"
                                              Disabled="@(approver.SortOrder == 1)"
                                              OnClick="@(() => MoveApproverUp(approver))" />
                                <MudIconButton Icon="@Icons.Material.Filled.ArrowDownward" 
                                              Size="Size.Small" 
                                              Color="Color.Default"
                                              Disabled="@(approver.SortOrder == MemoApprovers.Count)"
                                              OnClick="@(() => MoveApproverDown(approver))" />
                                <MudIconButton Icon="@Icons.Material.Filled.Delete" 
                                              Size="Size.Small" 
                                              Color="Color.Error"
                                              OnClick="@(() => RemoveApprover(approver))" />
                            </div>
                        </div>
                    }
                </div>
            }
            else
            {
                <div class="no-approvers">
                    <MudIcon Icon="@Icons.Material.Filled.PersonAdd" Size="Size.Large" Color="Color.Info" />
                    <h4>No Approvers Added</h4>
                    <p>Click "Add Approver" to start building your approval workflow.</p>
                </div>
            }
        </div>

        @if (Template != null && Template.Approvers != null && Template.Approvers.Any())
        {
            <div class="template-approvers">
                <div class="template-header">
                    <MudIcon Icon="@Icons.Material.Filled.Info" Color="Color.Info" />
                    <span>Template Default Approvers</span>
                </div>
                <div class="template-approver-list">
                    @foreach (var templateApprover in Template.Approvers)
                    {
                        <div class="template-approver-item">
                            <span class="template-role">@templateApprover.ApproverRoleTitle</span>
                            <span class="template-type">(@templateApprover.AllowType)</span>
                            @if (!MemoApprovers.Any(ma => ma.MemoApproverRoleId == templateApprover.MemoApproverRoleId))
                            {
                                <MudButton Size="Size.Small" 
                                          Variant="Variant.Text" 
                                          Color="Color.Primary"
                                          OnClick="@(() => AddTemplateApprover(templateApprover))">
                                    Add
                                </MudButton>
                            }
                        </div>
                    }
                </div>
            </div>
        }
    </div>
</div>

<!-- Add/Edit Approver Dialog -->
<MudDialog @bind-IsVisible="_showApproverDialog" Options="_dialogOptions">
    <TitleContent>
        <MudText Typo="Typo.h6">
            @(_editingApprover != null ? "Edit Approver" : "Add Approver")
        </MudText>
    </TitleContent>
    <DialogContent>
        <div class="approver-form">
            <div class="form-group">
                <MudTextField @bind-Value="_approverForm.Role"
                             Label="Role/Position"
                             Variant="Variant.Outlined"
                             Required="true"
                             FullWidth="true" />
            </div>

            <div class="form-group">
                <SfDropDownList DataSource="@_availableUsers"
                                Placeholder="Search and select user..."
                                @bind-Value="@_approverForm.UserId"
                                TValue="string"
                                TItem="UserDTO"
                                AllowFiltering="true"
                                FilterType="Syncfusion.Blazor.DropDowns.FilterType.Contains"
                                PopupWidth="100%"
                                FloatLabelType="FloatLabelType.Always">
                    <DropDownListFieldSettings Value="@nameof(UserDTO.UserId)"
                                               Text="@nameof(UserDTO.Name)">
                    </DropDownListFieldSettings>
                    <DropDownListEvents TValue="string" TItem="UserDTO" ValueChange="OnUserSelected"></DropDownListEvents>
                </SfDropDownList>
            </div>

            @if (!string.IsNullOrEmpty(_approverForm.Email))
            {
                <div class="form-group">
                    <MudTextField @bind-Value="_approverForm.Email"
                                 Label="Email"
                                 Variant="Variant.Outlined"
                                 ReadOnly="true"
                                 FullWidth="true" />
                </div>
            }

            @if (!string.IsNullOrEmpty(_approverForm.Department))
            {
                <div class="form-group">
                    <MudTextField @bind-Value="_approverForm.Department"
                                 Label="Department"
                                 Variant="Variant.Outlined"
                                 ReadOnly="true"
                                 FullWidth="true" />
                </div>
            }
        </div>
    </DialogContent>
    <DialogActions>
        <MudButton OnClick="@(() => _showApproverDialog = false)">Cancel</MudButton>
        <MudButton Color="Color.Primary" 
                  Variant="Variant.Filled" 
                  OnClick="@SaveApprover"
                  Disabled="@(!IsApproverFormValid())">
            @(_editingApprover != null ? "Update" : "Add")
        </MudButton>
    </DialogActions>
</MudDialog>

@code {
    [Parameter] public TemplateDto? Template { get; set; }
    [Parameter] public List<MemoApproverDto> MemoApprovers { get; set; } = new();
    [Parameter] public EventCallback<List<MemoApproverDto>> OnApproversChanged { get; set; }

    private List<UserDTO> _availableUsers = new();
    private bool _showApproverDialog = false;
    private MemoApproverDto? _editingApprover = null;
    private MemoApproverDto _approverForm = new();

    private MudBlazor.DialogOptions _dialogOptions = new()
    {
        CloseOnEscapeKey = true,
        MaxWidth = MaxWidth.Medium,
        FullWidth = true
    };

    protected override async Task OnInitializedAsync()
    {
        await LoadAvailableUsers();
    }

    private async Task LoadAvailableUsers()
    {
        try
        {
            _availableUsers = await AdminDataService.GetAllActiveAsync();
        }
        catch (Exception ex)
        {
            _availableUsers = new List<UserDTO>();
        }
    }

    private void OpenAddApproverDialog()
    {
        _editingApprover = null;
        _approverForm = new MemoApproverDto
        {
            SortOrder = (MemoApprovers.Any() ? MemoApprovers.Max(a => a.SortOrder ?? 0) : 0) + 1
        };
        _showApproverDialog = true;
    }

    private void EditApprover(MemoApproverDto approver)
    {
        _editingApprover = approver;
        _approverForm = new MemoApproverDto
        {
            Id = approver.Id,
            UserId = approver.UserId,
            User = approver.User,
            Email = approver.Email,
            Role = approver.Role,
            Department = approver.Department,
            SortOrder = approver.SortOrder,
            MemoApproverRoleId = approver.MemoApproverRoleId,
            AllowType = approver.AllowType
        };
        _showApproverDialog = true;
    }

    private async Task OnUserSelected(ChangeEventArgs<string, UserDTO> args)
    {
        if (args.ItemData != null)
        {
            _approverForm.UserId = args.ItemData.UserId;
            _approverForm.User = args.ItemData.Name;
            _approverForm.Email = args.ItemData.Email;
            _approverForm.Department = ""; // Could be populated from user data if available
        }
    }

    private bool IsApproverFormValid()
    {
        return !string.IsNullOrWhiteSpace(_approverForm.Role) && 
               !string.IsNullOrWhiteSpace(_approverForm.UserId);
    }

    private async Task SaveApprover()
    {
        if (!IsApproverFormValid()) return;

        if (_editingApprover != null)
        {
            // Update existing approver
            var existingApprover = MemoApprovers.FirstOrDefault(a => a.Id == _editingApprover.Id);
            if (existingApprover != null)
            {
                existingApprover.UserId = _approverForm.UserId;
                existingApprover.User = _approverForm.User;
                existingApprover.Email = _approverForm.Email;
                existingApprover.Role = _approverForm.Role;
                existingApprover.Department = _approverForm.Department;
            }
        }
        else
        {
            // Add new approver
            MemoApprovers.Add(_approverForm);
        }

        _showApproverDialog = false;
        await OnApproversChanged.InvokeAsync(MemoApprovers);
        StateHasChanged();
    }

    private async Task RemoveApprover(MemoApproverDto approver)
    {
        MemoApprovers.Remove(approver);
        
        // Reorder remaining approvers
        var orderedApprovers = MemoApprovers.OrderBy(a => a.SortOrder).ToList();
        for (int i = 0; i < orderedApprovers.Count; i++)
        {
            orderedApprovers[i].SortOrder = i + 1;
        }

        await OnApproversChanged.InvokeAsync(MemoApprovers);
        StateHasChanged();
    }

    private async Task MoveApproverUp(MemoApproverDto approver)
    {
        var currentOrder = approver.SortOrder ?? 0;
        if (currentOrder > 1)
        {
            var swapApprover = MemoApprovers.FirstOrDefault(a => a.SortOrder == currentOrder - 1);
            if (swapApprover != null)
            {
                approver.SortOrder = currentOrder - 1;
                swapApprover.SortOrder = currentOrder;
                await OnApproversChanged.InvokeAsync(MemoApprovers);
                StateHasChanged();
            }
        }
    }

    private async Task MoveApproverDown(MemoApproverDto approver)
    {
        var currentOrder = approver.SortOrder ?? 0;
        var maxOrder = MemoApprovers.Count;
        if (currentOrder < maxOrder)
        {
            var swapApprover = MemoApprovers.FirstOrDefault(a => a.SortOrder == currentOrder + 1);
            if (swapApprover != null)
            {
                approver.SortOrder = currentOrder + 1;
                swapApprover.SortOrder = currentOrder;
                await OnApproversChanged.InvokeAsync(MemoApprovers);
                StateHasChanged();
            }
        }
    }

    private async Task AddTemplateApprover(MemoTemplateApproverDto templateApprover)
    {
        var newApprover = new MemoApproverDto
        {
            Role = templateApprover.ApproverRoleTitle,
            AllowType = templateApprover.AllowType,
            MemoApproverRoleId = templateApprover.MemoApproverRoleId,
            SortOrder = (MemoApprovers.Any() ? MemoApprovers.Max(a => a.SortOrder ?? 0) : 0) + 1
        };

        MemoApprovers.Add(newApprover);
        await OnApproversChanged.InvokeAsync(MemoApprovers);
        StateHasChanged();
    }
}

<style>
    .approver-workflow-step {
        max-width: 1000px;
        margin: 0 auto;
        padding: 2rem;
    }

    .step-header {
        margin-bottom: 2rem;
        text-align: center;
    }

    .step-title {
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 0.5rem;
        font-size: 1.5rem;
        font-weight: 600;
        color: #2c3e50;
        margin-bottom: 0.5rem;
    }

    .step-icon {
        color: #4f8cff;
    }

    .step-description {
        color: #6c757d;
        font-size: 1rem;
        line-height: 1.5;
        margin: 0;
    }

    .step-content {
        background: #fff;
        border-radius: 12px;
        padding: 2rem;
        box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
        border: 1px solid #e9ecef;
    }

    .workflow-section {
        margin-bottom: 2rem;
    }

    .section-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 1.5rem;
        padding-bottom: 1rem;
        border-bottom: 1px solid #e9ecef;
    }

    .section-header h3 {
        margin: 0;
        color: #2c3e50;
        font-weight: 600;
    }

    .approvers-list {
        display: flex;
        flex-direction: column;
        gap: 1rem;
    }

    .approver-item {
        display: flex;
        align-items: center;
        gap: 1rem;
        padding: 1rem;
        background: #f8f9fa;
        border: 1px solid #e9ecef;
        border-radius: 8px;
        transition: all 0.2s ease;
    }

    .approver-item:hover {
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }

    .approver-order {
        flex-shrink: 0;
    }

    .order-number {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 32px;
        height: 32px;
        background: #4f8cff;
        color: white;
        border-radius: 50%;
        font-weight: 600;
        font-size: 0.9rem;
    }

    .approver-info {
        flex: 1;
    }

    .approver-name {
        font-size: 1rem;
        margin-bottom: 0.25rem;
    }

    .approver-details {
        font-size: 0.9rem;
        color: #6c757d;
    }

    .role {
        font-weight: 500;
        color: #495057;
    }

    .email, .department {
        color: #6c757d;
    }

    .approver-actions {
        display: flex;
        gap: 0.25rem;
        flex-shrink: 0;
    }

    .no-approvers {
        text-align: center;
        padding: 3rem;
        color: #6c757d;
    }

    .no-approvers h4 {
        margin: 1rem 0 0.5rem 0;
        color: #495057;
    }

    .no-approvers p {
        margin: 0;
        line-height: 1.5;
    }

    .template-approvers {
        background: #e3f2fd;
        border: 1px solid #bbdefb;
        border-radius: 8px;
        padding: 1.5rem;
    }

    .template-header {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        font-weight: 600;
        color: #1976d2;
        margin-bottom: 1rem;
    }

    .template-approver-list {
        display: flex;
        flex-direction: column;
        gap: 0.75rem;
    }

    .template-approver-item {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 0.75rem;
        background: #fff;
        border: 1px solid #bbdefb;
        border-radius: 6px;
    }

    .template-role {
        font-weight: 500;
        color: #1976d2;
    }

    .template-type {
        color: #6c757d;
        font-size: 0.9rem;
    }

    .approver-form {
        display: flex;
        flex-direction: column;
        gap: 1rem;
    }

    .form-group {
        margin-bottom: 1rem;
    }
</style>
