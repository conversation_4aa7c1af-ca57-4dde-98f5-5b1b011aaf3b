@using PSSmartMemo.DTO
@using Syncfusion.Blazor.RichTextEditor
@using Syncfusion.Blazor.Inputs

<div class="content-composition-step">
    <div class="step-header">
        <h2 class="step-title">
            <MudIcon Icon="@Icons.Material.Filled.Edit" Class="step-icon" />
            Content Composition
        </h2>
        <p class="step-description">
            Compose your memo content by filling in the sections below. Required sections are marked with an asterisk (*).
        </p>
    </div>

    <div class="step-content">
        @if (Template != null && MemoSections != null && MemoSections.Any())
        {
            <div class="sections-container">
                @foreach (var memoSection in MemoSections.OrderBy(s => s.SectionSortOrder))
                {
                    <div class="memo-section @(memoSection.IsRequired ? "required" : "")">
                        <div class="section-header">
                            <h3 class="section-title">
                                @memoSection.TemplateSectionTitle
                                @if (memoSection.IsRequired)
                                {
                                    <span class="required-indicator">*</span>
                                }
                            </h3>
                            @if (!string.IsNullOrEmpty(memoSection.TemplateContentHtml) && memoSection.IsWaterMark)
                            {
                                <div class="section-template">
                                    <MudIcon Icon="@Icons.Material.Filled.Info" Size="Size.Small" Color="Color.Info" />
                                    <span>Template guidance available</span>
                                    <MudButton Size="Size.Small"
                                              Variant="Variant.Text"
                                              Color="Color.Info"
                                              OnClick="@(() => ToggleTemplateView(memoSection.Id))">
                                        @(memoSection.isOpen ? "Hide" : "Show") Template
                                    </MudButton>
                                </div>
                            }
                        </div>

                        @if (memoSection.isOpen && !string.IsNullOrEmpty(memoSection.TemplateContentHtml))
                        {
                            <div class="template-content">
                                <div class="template-label">Template Content:</div>
                                <div class="template-html">
                                    @((MarkupString)memoSection.TemplateContentHtml)
                                </div>
                            </div>
                        }

                        <div class="section-editor">
                            <SfRichTextEditor @bind-Value="@memoSection.ContentHtml"
                                            Height="300px"
                                            Placeholder="@GetSectionPlaceholder(memoSection)"
                                            CssClass="memo-editor">
                                <RichTextEditorToolbarSettings Items="@_toolbarItems" />
                                <RichTextEditorEvents ValueChange="@(() => OnSectionContentChanged(memoSection))" />
                            </SfRichTextEditor>
                        </div>

                        @if (memoSection.IsRequired && string.IsNullOrWhiteSpace(memoSection.ContentHtml))
                        {
                            <div class="validation-warning">
                                <MudIcon Icon="@Icons.Material.Filled.Warning" Size="Size.Small" />
                                This section is required and cannot be empty.
                            </div>
                        }
                    </div>
                }
            </div>

            <div class="content-summary">
                <div class="summary-header">
                    <MudIcon Icon="@Icons.Material.Filled.Assessment" Color="Color.Primary" />
                    <span>Content Summary</span>
                </div>
                <div class="summary-stats">
                    <div class="stat-item">
                        <strong>Total Sections:</strong> @MemoSections.Count
                    </div>
                    <div class="stat-item">
                        <strong>Completed:</strong> @GetCompletedSectionsCount() / @MemoSections.Count
                    </div>
                    <div class="stat-item">
                        <strong>Required Sections:</strong> @GetRequiredSectionsCount()
                    </div>
                    @if (GetMissingSectionsCount() > 0)
                    {
                        <div class="stat-item warning">
                            <strong>Missing Required:</strong> @GetMissingSectionsCount()
                        </div>
                    }
                </div>
            </div>
        }
        else if (Template == null)
        {
            <div class="no-template">
                <MudIcon Icon="@Icons.Material.Filled.Warning" Size="Size.Large" Color="Color.Warning" />
                <h3>No Template Selected</h3>
                <p>Please go back to Step 1 and select a template to begin composing your memo content.</p>
            </div>
        }
        else
        {
            <div class="no-sections">
                <MudIcon Icon="@Icons.Material.Filled.Info" Size="Size.Large" Color="Color.Info" />
                <h3>No Sections Available</h3>
                <p>The selected template does not have any content sections defined.</p>
            </div>
        }
    </div>
</div>

@code {
    [Parameter] public TemplateDto? Template { get; set; }
    [Parameter] public List<MemoSectionDto> MemoSections { get; set; } = new();
    [Parameter] public EventCallback<List<MemoSectionDto>> OnSectionsChanged { get; set; }

    private List<ToolbarItemModel> _toolbarItems = new()
    {
        new ToolbarItemModel() { Command = ToolbarCommand.Bold },
        new ToolbarItemModel() { Command = ToolbarCommand.Italic },
        new ToolbarItemModel() { Command = ToolbarCommand.Underline },
        new ToolbarItemModel() { Command = ToolbarCommand.Separator },
        new ToolbarItemModel() { Command = ToolbarCommand.FontName },
        new ToolbarItemModel() { Command = ToolbarCommand.FontSize },
        new ToolbarItemModel() { Command = ToolbarCommand.Separator },
        new ToolbarItemModel() { Command = ToolbarCommand.FontColor },
        new ToolbarItemModel() { Command = ToolbarCommand.BackgroundColor },
        new ToolbarItemModel() { Command = ToolbarCommand.Separator },
        new ToolbarItemModel() { Command = ToolbarCommand.Formats },
        new ToolbarItemModel() { Command = ToolbarCommand.Alignments },
        new ToolbarItemModel() { Command = ToolbarCommand.Separator },
        new ToolbarItemModel() { Command = ToolbarCommand.NumberFormatList },
        new ToolbarItemModel() { Command = ToolbarCommand.BulletFormatList },
        new ToolbarItemModel() { Command = ToolbarCommand.Separator },
        new ToolbarItemModel() { Command = ToolbarCommand.CreateLink },
        new ToolbarItemModel() { Command = ToolbarCommand.Image },
        new ToolbarItemModel() { Command = ToolbarCommand.CreateTable },
        new ToolbarItemModel() { Command = ToolbarCommand.Separator },
        new ToolbarItemModel() { Command = ToolbarCommand.Undo },
        new ToolbarItemModel() { Command = ToolbarCommand.Redo }
    };

    private async Task OnSectionContentChanged(MemoSectionDto section)
    {
        await OnSectionsChanged.InvokeAsync(MemoSections);
    }

    private void ToggleTemplateView(Guid sectionId)
    {
        var section = MemoSections.FirstOrDefault(s => s.Id == sectionId);
        if (section != null)
        {
            section.isOpen = !section.isOpen;
            StateHasChanged();
        }
    }

    private string GetSectionPlaceholder(MemoSectionDto section)
    {
        if (section.IsRequired)
        {
            return $"Enter content for {section.TemplateSectionTitle} (Required)...";
        }
        return $"Enter content for {section.TemplateSectionTitle} (Optional)...";
    }

    private int GetCompletedSectionsCount()
    {
        return MemoSections.Count(s => !string.IsNullOrWhiteSpace(s.ContentHtml));
    }

    private int GetRequiredSectionsCount()
    {
        return MemoSections.Count(s => s.IsRequired);
    }

    private int GetMissingSectionsCount()
    {
        return MemoSections.Count(s => s.IsRequired && string.IsNullOrWhiteSpace(s.ContentHtml));
    }
}

<style>
    .content-composition-step {
        max-width: 1200px;
        margin: 0 auto;
        padding: 2rem;
    }

    .step-header {
        margin-bottom: 2rem;
        text-align: center;
    }

    .step-title {
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 0.5rem;
        font-size: 1.5rem;
        font-weight: 600;
        color: #2c3e50;
        margin-bottom: 0.5rem;
    }

    .step-icon {
        color: #4f8cff;
    }

    .step-description {
        color: #6c757d;
        font-size: 1rem;
        line-height: 1.5;
        margin: 0;
    }

    .step-content {
        background: #fff;
        border-radius: 12px;
        padding: 2rem;
        box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
        border: 1px solid #e9ecef;
    }

    .sections-container {
        display: flex;
        flex-direction: column;
        gap: 2rem;
    }

    .memo-section {
        border: 1px solid #e9ecef;
        border-radius: 8px;
        overflow: hidden;
    }

    .memo-section.required {
        border-left: 4px solid #dc3545;
    }

    .section-header {
        background: #f8f9fa;
        padding: 1rem;
        border-bottom: 1px solid #e9ecef;
    }

    .section-title {
        margin: 0 0 0.5rem 0;
        font-size: 1.1rem;
        font-weight: 600;
        color: #2c3e50;
        display: flex;
        align-items: center;
        gap: 0.25rem;
    }

    .required-indicator {
        color: #dc3545;
        font-weight: bold;
    }

    .section-template {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        font-size: 0.9rem;
        color: #6c757d;
    }

    .template-content {
        background: #e3f2fd;
        padding: 1rem;
        border-bottom: 1px solid #e9ecef;
    }

    .template-label {
        font-weight: 600;
        color: #1976d2;
        margin-bottom: 0.5rem;
        font-size: 0.9rem;
    }

    .template-html {
        background: #fff;
        padding: 1rem;
        border-radius: 4px;
        border: 1px solid #bbdefb;
        font-size: 0.9rem;
    }

    .section-editor {
        padding: 1rem;
    }

    .validation-warning {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        background: #fff3cd;
        color: #856404;
        padding: 0.75rem 1rem;
        font-size: 0.9rem;
        border-top: 1px solid #e9ecef;
    }

    .content-summary {
        margin-top: 2rem;
        background: #f8f9fa;
        border: 1px solid #e9ecef;
        border-radius: 8px;
        padding: 1.5rem;
    }

    .summary-header {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        font-weight: 600;
        color: #2c3e50;
        margin-bottom: 1rem;
    }

    .summary-stats {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 1rem;
    }

    .stat-item {
        color: #495057;
        font-size: 0.9rem;
    }

    .stat-item.warning {
        color: #856404;
        font-weight: 600;
    }

    .no-template,
    .no-sections {
        text-align: center;
        padding: 3rem;
        color: #6c757d;
    }

    .no-template h3,
    .no-sections h3 {
        margin: 1rem 0 0.5rem 0;
        color: #495057;
    }

    .no-template p,
    .no-sections p {
        margin: 0;
        line-height: 1.5;
    }
</style>
