/* Memo Builder Global Styles */

/* Professional color palette */
:root {
    --memo-primary: #4f8cff;
    --memo-secondary: #1abc9c;
    --memo-success: #28a745;
    --memo-warning: #ffc107;
    --memo-error: #dc3545;
    --memo-info: #17a2b8;
    --memo-light: #f8f9fa;
    --memo-dark: #2c3e50;
    --memo-muted: #6c757d;
    --memo-border: #e9ecef;
    --memo-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
    --memo-shadow-hover: 0 4px 16px rgba(0, 0, 0, 0.15);
    --memo-gradient: linear-gradient(135deg, #4f8cff 0%, #1abc9c 100%);
    --memo-border-radius: 8px;
    --memo-border-radius-lg: 12px;
}

/* Global memo builder container */
.memo-builder-wrapper {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', <PERSON><PERSON>, 'Helvetica Neue', <PERSON><PERSON>, sans-serif;
    line-height: 1.6;
    color: var(--memo-dark);
}

/* Enhanced card styles */
.memo-card {
    background: #fff;
    border: 1px solid var(--memo-border);
    border-radius: var(--memo-border-radius-lg);
    box-shadow: var(--memo-shadow);
    transition: all 0.3s ease;
}

.memo-card:hover {
    box-shadow: var(--memo-shadow-hover);
    transform: translateY(-2px);
}

/* Professional button styles */
.memo-btn {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: var(--memo-border-radius);
    font-weight: 600;
    text-decoration: none;
    transition: all 0.2s ease;
    cursor: pointer;
    font-size: 0.9rem;
}

.memo-btn-primary {
    background: var(--memo-gradient);
    color: white;
}

.memo-btn-primary:hover {
    background: linear-gradient(135deg, #1abc9c 0%, #4f8cff 100%);
    transform: translateY(-1px);
    box-shadow: var(--memo-shadow-hover);
}

.memo-btn-outline {
    background: transparent;
    color: var(--memo-primary);
    border: 2px solid var(--memo-primary);
}

.memo-btn-outline:hover {
    background: var(--memo-primary);
    color: white;
}

/* Form elements */
.memo-form-group {
    margin-bottom: 1.5rem;
}

.memo-form-label {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-weight: 600;
    color: var(--memo-dark);
    margin-bottom: 0.5rem;
    font-size: 0.9rem;
}

.memo-form-control {
    width: 100%;
    padding: 0.75rem;
    border: 2px solid var(--memo-border);
    border-radius: var(--memo-border-radius);
    font-size: 0.9rem;
    transition: border-color 0.2s ease;
}

.memo-form-control:focus {
    outline: none;
    border-color: var(--memo-primary);
    box-shadow: 0 0 0 3px rgba(79, 140, 255, 0.1);
}

/* Status indicators */
.memo-status {
    display: inline-flex;
    align-items: center;
    gap: 0.25rem;
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 600;
}

.memo-status-success {
    background: rgba(40, 167, 69, 0.1);
    color: var(--memo-success);
    border: 1px solid rgba(40, 167, 69, 0.2);
}

.memo-status-warning {
    background: rgba(255, 193, 7, 0.1);
    color: #856404;
    border: 1px solid rgba(255, 193, 7, 0.2);
}

.memo-status-error {
    background: rgba(220, 53, 69, 0.1);
    color: var(--memo-error);
    border: 1px solid rgba(220, 53, 69, 0.2);
}

.memo-status-info {
    background: rgba(23, 162, 184, 0.1);
    color: var(--memo-info);
    border: 1px solid rgba(23, 162, 184, 0.2);
}

/* Progress indicators */
.memo-progress {
    width: 100%;
    height: 8px;
    background: var(--memo-light);
    border-radius: 4px;
    overflow: hidden;
}

.memo-progress-bar {
    height: 100%;
    background: var(--memo-gradient);
    transition: width 0.3s ease;
}

/* Responsive design */
@media (max-width: 768px) {
    .memo-builder-wrapper {
        padding: 0.5rem;
    }
    
    .memo-card {
        margin: 0.5rem 0;
        border-radius: var(--memo-border-radius);
    }
    
    .memo-btn {
        width: 100%;
        justify-content: center;
        padding: 1rem;
    }
    
    .memo-form-control {
        font-size: 16px; /* Prevent zoom on iOS */
    }
}

/* Animation utilities */
.memo-fade-in {
    animation: memoFadeIn 0.3s ease-in-out;
}

.memo-slide-up {
    animation: memoSlideUp 0.3s ease-out;
}

@keyframes memoFadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

@keyframes memoSlideUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Accessibility improvements */
.memo-sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

/* Focus styles for keyboard navigation */
.memo-focusable:focus {
    outline: 2px solid var(--memo-primary);
    outline-offset: 2px;
}

/* Loading states */
.memo-loading {
    position: relative;
    pointer-events: none;
    opacity: 0.6;
}

.memo-loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid var(--memo-border);
    border-top-color: var(--memo-primary);
    border-radius: 50%;
    animation: memoSpin 1s linear infinite;
}

@keyframes memoSpin {
    to {
        transform: rotate(360deg);
    }
}

/* Print styles */
@media print {
    .memo-builder-navigation,
    .memo-stepper,
    .memo-btn {
        display: none !important;
    }
    
    .memo-card {
        box-shadow: none;
        border: 1px solid #ccc;
    }
    
    .memo-builder-wrapper {
        font-size: 12pt;
        line-height: 1.4;
    }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    :root {
        --memo-border: #000;
        --memo-muted: #000;
    }
    
    .memo-card {
        border: 2px solid #000;
    }
    
    .memo-btn-primary {
        background: #000;
        border: 2px solid #000;
    }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}
