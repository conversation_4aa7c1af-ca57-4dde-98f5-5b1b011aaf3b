@using PSSmartMemo.DTO

<div class="final-actions-step">
    <div class="step-header">
        <h2 class="step-title">
            <MudIcon Icon="@Icons.Material.Filled.Send" Class="step-icon" />
            Review & Submit
        </h2>
        <p class="step-description">
            Review your memo details and choose to save as draft or submit for approval.
        </p>
    </div>

    <div class="step-content">
        <!-- Memo Summary -->
        <div class="memo-summary">
            <div class="summary-header">
                <MudIcon Icon="@Icons.Material.Filled.Summarize" Color="Color.Primary" />
                <h3>Memo Summary</h3>
            </div>
            
            <div class="summary-content">
                <div class="summary-item">
                    <strong>Title:</strong>
                    <span>@MemoState.Title</span>
                </div>
                
                @if (Template != null)
                {
                    <div class="summary-item">
                        <strong>Template:</strong>
                        <span>@Template.TemplateTitle</span>
                    </div>
                    
                    <div class="summary-item">
                        <strong>Type:</strong>
                        <span>@Template.MemoType</span>
                    </div>
                }
                
                <div class="summary-item">
                    <strong>Sections:</strong>
                    <span>@MemoState.Sections.Count sections (@GetCompletedSectionsCount() completed)</span>
                </div>
                
                <div class="summary-item">
                    <strong>Approvers:</strong>
                    <span>@MemoState.Approvers.Count approvers configured</span>
                </div>
                
                <div class="summary-item">
                    <strong>Attachments:</strong>
                    <span>@MemoState.Attachments.Count files attached</span>
                </div>
            </div>
        </div>

        <!-- Validation Status -->
        <div class="validation-status">
            <div class="validation-header">
                <MudIcon Icon="@GetValidationIcon()" Color="@GetValidationColor()" />
                <h3>Validation Status</h3>
            </div>
            
            <div class="validation-content">
                @if (_validationResults.Any())
                {
                    @foreach (var result in _validationResults)
                    {
                        <div class="validation-item @result.Type.ToString().ToLower()">
                            <MudIcon Icon="@GetValidationItemIcon(result.Type)" 
                                    Color="@GetValidationItemColor(result.Type)" 
                                    Size="Size.Small" />
                            <span>@result.Message</span>
                        </div>
                    }
                }
                else
                {
                    <div class="validation-item success">
                        <MudIcon Icon="@Icons.Material.Filled.CheckCircle" Color="Color.Success" Size="Size.Small" />
                        <span>All validations passed. Ready for submission.</span>
                    </div>
                }
            </div>
        </div>

        <!-- Content Preview -->
        <div class="content-preview">
            <div class="preview-header">
                <MudIcon Icon="@Icons.Material.Filled.Preview" Color="Color.Primary" />
                <h3>Content Preview</h3>
                <MudButton Size="Size.Small" 
                          Variant="Variant.Text" 
                          Color="Color.Primary"
                          OnClick="@(() => _showFullPreview = !_showFullPreview)">
                    @(_showFullPreview ? "Hide" : "Show") Full Preview
                </MudButton>
            </div>
            
            @if (_showFullPreview)
            {
                <div class="preview-content">
                    <div class="memo-header-preview">
                        <h2>@MemoState.Title</h2>
                        @if (Template != null)
                        {
                            <p class="memo-type">@Template.MemoType</p>
                        }
                    </div>
                    
                    @foreach (var memoSection in MemoState.Sections.OrderBy(s => s.SectionSortOrder))
                    {
                        <div class="section-preview">
                            <h4>@memoSection.TemplateSectionTitle</h4>
                            @if (!string.IsNullOrWhiteSpace(memoSection.ContentHtml))
                            {
                                <div class="section-content">
                                    @((MarkupString)memoSection.ContentHtml)
                                </div>
                            }
                            else
                            {
                                <div class="empty-section">
                                    <em>No content provided</em>
                                </div>
                            }
                        </div>
                    }
                    
                    @if (MemoState.Attachments.Any())
                    {
                        <div class="attachments-preview">
                            <h4>Attachments</h4>
                            <ul>
                                @foreach (var attachment in MemoState.Attachments)
                                {
                                    <li>
                                        @attachment.Name (@attachment.Size)
                                        @if (!string.IsNullOrEmpty(attachment.Description))
                                        {
                                            <span> - @attachment.Description</span>
                                        }
                                    </li>
                                }
                            </ul>
                        </div>
                    }
                </div>
            }
        </div>

        <!-- Action Buttons -->
        <div class="action-section">
            <div class="action-header">
                <h3>Choose Action</h3>
                <p>You can save your memo as a draft to continue working on it later, or submit it for approval.</p>
            </div>
            
            <div class="action-buttons">
                <MudButton Variant="Variant.Outlined" 
                          Color="Color.Default" 
                          Size="Size.Large"
                          StartIcon="@Icons.Material.Filled.Save"
                          OnClick="@OnSaveAsDraftClicked"
                          Class="action-button">
                    <div class="button-content">
                        <div class="button-title">Save as Draft</div>
                        <div class="button-subtitle">Continue working later</div>
                    </div>
                </MudButton>
                
                <MudButton Variant="Variant.Filled" 
                          Color="Color.Primary" 
                          Size="Size.Large"
                          StartIcon="@Icons.Material.Filled.Send"
                          OnClick="@OnSubmitForApprovalClicked"
                          Disabled="@(!CanSubmitForApproval())"
                          Class="action-button">
                    <div class="button-content">
                        <div class="button-title">Submit for Approval</div>
                        <div class="button-subtitle">Send to approvers</div>
                    </div>
                </MudButton>
            </div>
            
            @if (!CanSubmitForApproval())
            {
                <div class="submission-warning">
                    <MudIcon Icon="@Icons.Material.Filled.Warning" Color="Color.Warning" Size="Size.Small" />
                    <span>Please resolve validation issues before submitting for approval.</span>
                </div>
            }
        </div>
    </div>
</div>

@code {
    [Parameter] public PSSmartMemo.Components.Pages.Memo.MemoBuilderWizard.MemoBuilderState MemoState { get; set; } = new();
    [Parameter] public TemplateDto? Template { get; set; }
    [Parameter] public EventCallback OnSaveAsDraft { get; set; }
    [Parameter] public EventCallback OnSubmitForApproval { get; set; }

    private bool _showFullPreview = false;
    private List<ValidationResult> _validationResults = new();

    protected override void OnParametersSet()
    {
        ValidateMemo();
    }

    private void ValidateMemo()
    {
        _validationResults.Clear();

        // Validate title
        if (string.IsNullOrWhiteSpace(MemoState.Title))
        {
            _validationResults.Add(new ValidationResult(ValidationType.Error, "Memo title is required."));
        }

        // Validate template selection
        if (!MemoState.TemplateId.HasValue)
        {
            _validationResults.Add(new ValidationResult(ValidationType.Error, "Template selection is required."));
        }

        // Validate required sections
        var missingSections = MemoState.Sections.Where(s => s.IsRequired && string.IsNullOrWhiteSpace(s.ContentHtml)).ToList();
        if (missingSections.Any())
        {
            _validationResults.Add(new ValidationResult(ValidationType.Error, $"{missingSections.Count} required section(s) are empty."));
        }

        // Validate approvers
        if (!MemoState.Approvers.Any())
        {
            _validationResults.Add(new ValidationResult(ValidationType.Warning, "No approvers configured. Memo will require manual routing."));
        }
        else
        {
            var incompleteApprovers = MemoState.Approvers.Where(a => string.IsNullOrWhiteSpace(a.UserId)).ToList();
            if (incompleteApprovers.Any())
            {
                _validationResults.Add(new ValidationResult(ValidationType.Error, $"{incompleteApprovers.Count} approver(s) do not have users assigned."));
            }
        }

        // Validate attachments if template allows
        if (Template?.MemoTemplateAttachmentAllowed == true)
        {
            if (MemoState.Attachments.Count > Template.MemoTemplateAttachmentFileCountAllowed)
            {
                _validationResults.Add(new ValidationResult(ValidationType.Error, $"Too many attachments. Maximum allowed: {Template.MemoTemplateAttachmentFileCountAllowed}"));
            }
        }

        // Add info messages
        if (!_validationResults.Any(v => v.Type == ValidationType.Error))
        {
            _validationResults.Add(new ValidationResult(ValidationType.Info, "Memo is ready for submission."));
        }
    }

    private int GetCompletedSectionsCount()
    {
        return MemoState.Sections.Count(s => !string.IsNullOrWhiteSpace(s.ContentHtml));
    }

    private bool CanSubmitForApproval()
    {
        return !_validationResults.Any(v => v.Type == ValidationType.Error);
    }

    private string GetValidationIcon()
    {
        if (_validationResults.Any(v => v.Type == ValidationType.Error))
            return Icons.Material.Filled.Error;
        if (_validationResults.Any(v => v.Type == ValidationType.Warning))
            return Icons.Material.Filled.Warning;
        return Icons.Material.Filled.CheckCircle;
    }

    private Color GetValidationColor()
    {
        if (_validationResults.Any(v => v.Type == ValidationType.Error))
            return Color.Error;
        if (_validationResults.Any(v => v.Type == ValidationType.Warning))
            return Color.Warning;
        return Color.Success;
    }

    private string GetValidationItemIcon(ValidationType type)
    {
        return type switch
        {
            ValidationType.Error => Icons.Material.Filled.Error,
            ValidationType.Warning => Icons.Material.Filled.Warning,
            ValidationType.Info => Icons.Material.Filled.Info,
            ValidationType.Success => Icons.Material.Filled.CheckCircle,
            _ => Icons.Material.Filled.Info
        };
    }

    private Color GetValidationItemColor(ValidationType type)
    {
        return type switch
        {
            ValidationType.Error => Color.Error,
            ValidationType.Warning => Color.Warning,
            ValidationType.Info => Color.Info,
            ValidationType.Success => Color.Success,
            _ => Color.Default
        };
    }

    private async Task OnSaveAsDraftClicked()
    {
        await OnSaveAsDraft.InvokeAsync();
    }

    private async Task OnSubmitForApprovalClicked()
    {
        if (CanSubmitForApproval())
        {
            await OnSubmitForApproval.InvokeAsync();
        }
    }

    public enum ValidationType
    {
        Error,
        Warning,
        Info,
        Success
    }

    public class ValidationResult
    {
        public ValidationType Type { get; set; }
        public string Message { get; set; }

        public ValidationResult(ValidationType type, string message)
        {
            Type = type;
            Message = message;
        }
    }
}

<style>
    .final-actions-step {
        max-width: 1000px;
        margin: 0 auto;
        padding: 2rem;
    }

    .step-header {
        margin-bottom: 2rem;
        text-align: center;
    }

    .step-title {
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 0.5rem;
        font-size: 1.5rem;
        font-weight: 600;
        color: #2c3e50;
        margin-bottom: 0.5rem;
    }

    .step-icon {
        color: #4f8cff;
    }

    .step-description {
        color: #6c757d;
        font-size: 1rem;
        line-height: 1.5;
        margin: 0;
    }

    .step-content {
        background: #fff;
        border-radius: 12px;
        padding: 2rem;
        box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
        border: 1px solid #e9ecef;
        display: flex;
        flex-direction: column;
        gap: 2rem;
    }

    .memo-summary,
    .validation-status,
    .content-preview,
    .action-section {
        background: #f8f9fa;
        border: 1px solid #e9ecef;
        border-radius: 8px;
        padding: 1.5rem;
    }

    .summary-header,
    .validation-header,
    .preview-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 1rem;
        padding-bottom: 0.5rem;
        border-bottom: 1px solid #e9ecef;
    }

    .summary-header h3,
    .validation-header h3,
    .preview-header h3 {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        margin: 0;
        color: #2c3e50;
        font-weight: 600;
    }

    .summary-content {
        display: flex;
        flex-direction: column;
        gap: 0.75rem;
    }

    .summary-item {
        display: flex;
        gap: 0.5rem;
        font-size: 0.95rem;
    }

    .summary-item strong {
        min-width: 120px;
        color: #495057;
    }

    .summary-item span {
        color: #6c757d;
    }

    .validation-content {
        display: flex;
        flex-direction: column;
        gap: 0.5rem;
    }

    .validation-item {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        padding: 0.5rem;
        border-radius: 4px;
        font-size: 0.9rem;
    }

    .validation-item.error {
        background: #f8d7da;
        color: #721c24;
        border: 1px solid #f5c6cb;
    }

    .validation-item.warning {
        background: #fff3cd;
        color: #856404;
        border: 1px solid #ffeaa7;
    }

    .validation-item.info {
        background: #d1ecf1;
        color: #0c5460;
        border: 1px solid #bee5eb;
    }

    .validation-item.success {
        background: #d4edda;
        color: #155724;
        border: 1px solid #c3e6cb;
    }

    .preview-content {
        margin-top: 1rem;
        padding: 1.5rem;
        background: #fff;
        border: 1px solid #e9ecef;
        border-radius: 6px;
        max-height: 500px;
        overflow-y: auto;
    }

    .memo-header-preview {
        margin-bottom: 2rem;
        padding-bottom: 1rem;
        border-bottom: 2px solid #e9ecef;
    }

    .memo-header-preview h2 {
        margin: 0 0 0.5rem 0;
        color: #2c3e50;
    }

    .memo-type {
        margin: 0;
        color: #6c757d;
        font-style: italic;
    }

    .section-preview {
        margin-bottom: 1.5rem;
    }

    .section-preview h4 {
        margin: 0 0 0.75rem 0;
        color: #495057;
        font-weight: 600;
        border-bottom: 1px solid #e9ecef;
        padding-bottom: 0.25rem;
    }

    .section-content {
        color: #495057;
        line-height: 1.6;
    }

    .empty-section {
        color: #6c757d;
        font-style: italic;
        padding: 0.5rem;
        background: #f8f9fa;
        border-radius: 4px;
    }

    .attachments-preview {
        margin-top: 1.5rem;
        padding-top: 1rem;
        border-top: 1px solid #e9ecef;
    }

    .attachments-preview h4 {
        margin: 0 0 0.75rem 0;
        color: #495057;
        font-weight: 600;
    }

    .attachments-preview ul {
        margin: 0;
        padding-left: 1.5rem;
        color: #495057;
    }

    .action-section {
        text-align: center;
    }

    .action-header {
        margin-bottom: 2rem;
    }

    .action-header h3 {
        margin: 0 0 0.5rem 0;
        color: #2c3e50;
        font-weight: 600;
    }

    .action-header p {
        margin: 0;
        color: #6c757d;
        line-height: 1.5;
    }

    .action-buttons {
        display: flex;
        gap: 2rem;
        justify-content: center;
        margin-bottom: 1rem;
    }

    .action-button {
        min-width: 200px;
        height: auto;
        padding: 1rem 1.5rem;
    }

    .button-content {
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 0.25rem;
    }

    .button-title {
        font-weight: 600;
        font-size: 1rem;
    }

    .button-subtitle {
        font-size: 0.8rem;
        opacity: 0.8;
    }

    .submission-warning {
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 0.5rem;
        color: #856404;
        font-size: 0.9rem;
        background: #fff3cd;
        padding: 0.75rem;
        border-radius: 6px;
        border: 1px solid #ffeaa7;
    }

    /* Mobile styles moved to external CSS file */
</style>
