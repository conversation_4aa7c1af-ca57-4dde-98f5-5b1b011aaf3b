@using PSSmartMemo.DTO
@using Microsoft.AspNetCore.Components.Forms
@inject IJSRuntime JSRuntime

<div class="document-attachment-step">
    <div class="step-header">
        <h2 class="step-title">
            <MudIcon Icon="@Icons.Material.Filled.AttachFile" Class="step-icon" />
            Document Attachments
        </h2>
        <p class="step-description">
            Add supporting documents to your memo. Check template restrictions for file types and sizes.
        </p>
    </div>

    <div class="step-content">
        @if (Template != null)
        {
            <div class="attachment-rules">
                <div class="rules-header">
                    <MudIcon Icon="@Icons.Material.Filled.Rule" Color="Color.Info" />
                    <span>Attachment Rules</span>
                </div>
                <div class="rules-content">
                    @if (Template.MemoTemplateAttachmentAllowed)
                    {
                        <div class="rule-item">
                            <MudIcon Icon="@Icons.Material.Filled.CheckCircle" Color="Color.Success" Size="Size.Small" />
                            <span>Attachments are allowed for this template</span>
                        </div>
                        <div class="rule-item">
                            <MudIcon Icon="@Icons.Material.Filled.Numbers" Color="Color.Info" Size="Size.Small" />
                            <span>Maximum files: @Template.MemoTemplateAttachmentFileCountAllowed</span>
                        </div>
                        <div class="rule-item">
                            <MudIcon Icon="@Icons.Material.Filled.Storage" Color="Color.Info" Size="Size.Small" />
                            <span>Maximum size per file: @Template.MemoTemplateAttachmentPerFileSizeMbAllowed MB</span>
                        </div>
                        <div class="rule-item">
                            <MudIcon Icon="@Icons.Material.Filled.Description" Color="Color.Info" Size="Size.Small" />
                            <span>Supported formats: PDF, DOC, DOCX, XLS, XLSX, PNG, JPG, JPEG</span>
                        </div>
                    }
                    else
                    {
                        <div class="rule-item warning">
                            <MudIcon Icon="@Icons.Material.Filled.Block" Color="Color.Warning" Size="Size.Small" />
                            <span>Attachments are not allowed for this template</span>
                        </div>
                    }
                </div>
            </div>

            @if (Template.MemoTemplateAttachmentAllowed)
            {
                <div class="upload-section">
                    <div class="upload-area @(_isDragOver ? "drag-over" : "")"
                         @ondragover="@OnDragOver"
                         @ondragover:preventDefault="true"
                         @ondragleave="@OnDragLeave"
                         @ondrop="@OnDrop"
                         @ondrop:preventDefault="true">
                        
                        <MudFileUpload T="IReadOnlyList<IBrowserFile>"
                                      Accept=".pdf,.doc,.docx,.xls,.xlsx,.png,.jpg,.jpeg"
                                      FilesChanged="@OnFilesSelected"
                                      MaximumFileCount="@((int)Template.MemoTemplateAttachmentFileCountAllowed)"
                                      MaximumFileSize="@((long)(Template.MemoTemplateAttachmentPerFileSizeMbAllowed * 1024 * 1024))"
                                      Class="upload-input">
                            <ActivatorContent>
                                <div class="upload-content">
                                    <MudIcon Icon="@Icons.Material.Filled.CloudUpload" Size="Size.Large" Color="Color.Primary" />
                                    <h4>Drop files here or click to browse</h4>
                                    <p>Select up to @Template.MemoTemplateAttachmentFileCountAllowed files, max @Template.MemoTemplateAttachmentPerFileSizeMbAllowed MB each</p>
                                    <MudButton Variant="Variant.Outlined" 
                                              Color="Color.Primary" 
                                              StartIcon="@Icons.Material.Filled.FolderOpen">
                                        Browse Files
                                    </MudButton>
                                </div>
                            </ActivatorContent>
                        </MudFileUpload>
                    </div>

                    @if (_uploadErrors.Any())
                    {
                        <div class="upload-errors">
                            @foreach (var error in _uploadErrors)
                            {
                                <div class="error-item">
                                    <MudIcon Icon="@Icons.Material.Filled.Error" Color="Color.Error" Size="Size.Small" />
                                    <span>@error</span>
                                </div>
                            }
                        </div>
                    }
                </div>

                <div class="attachments-list">
                    <div class="list-header">
                        <h3>Attached Files (@MemoAttachments.Count/@Template.MemoTemplateAttachmentFileCountAllowed)</h3>
                    </div>

                    @if (MemoAttachments.Any())
                    {
                        <div class="attachments-grid">
                            @foreach (var attachment in MemoAttachments)
                            {
                                <div class="attachment-item">
                                    <div class="attachment-icon">
                                        <MudIcon Icon="@GetFileIcon(attachment.Type)" 
                                                Color="@GetFileIconColor(attachment.Type)" 
                                                Size="Size.Large" />
                                    </div>
                                    
                                    <div class="attachment-info">
                                        <div class="attachment-name" title="@attachment.Name">
                                            @attachment.Name
                                        </div>
                                        <div class="attachment-details">
                                            <span class="file-size">@attachment.Size</span>
                                            <span class="file-type">• @attachment.Type.ToUpper()</span>
                                        </div>
                                        @if (!string.IsNullOrEmpty(attachment.Description))
                                        {
                                            <div class="attachment-description">
                                                @attachment.Description
                                            </div>
                                        }
                                    </div>

                                    <div class="attachment-actions">
                                        <MudIconButton Icon="@Icons.Material.Filled.Edit" 
                                                      Size="Size.Small" 
                                                      Color="Color.Primary"
                                                      OnClick="@(() => EditAttachment(attachment))" />
                                        <MudIconButton Icon="@Icons.Material.Filled.Delete" 
                                                      Size="Size.Small" 
                                                      Color="Color.Error"
                                                      OnClick="@(() => RemoveAttachment(attachment))" />
                                    </div>
                                </div>
                            }
                        </div>
                    }
                    else
                    {
                        <div class="no-attachments">
                            <MudIcon Icon="@Icons.Material.Filled.AttachFile" Size="Size.Large" Color="Color.Default" />
                            <h4>No Files Attached</h4>
                            <p>Upload files using the area above to add supporting documents to your memo.</p>
                        </div>
                    }
                </div>
            }
        }
        else
        {
            <div class="no-template">
                <MudIcon Icon="@Icons.Material.Filled.Warning" Size="Size.Large" Color="Color.Warning" />
                <h3>No Template Selected</h3>
                <p>Please go back to Step 1 and select a template to configure attachments.</p>
            </div>
        }
    </div>
</div>

<!-- Edit Attachment Dialog -->
<MudDialog @bind-IsVisible="_showEditDialog" Options="_dialogOptions">
    <TitleContent>
        <MudText Typo="Typo.h6">Edit Attachment</MudText>
    </TitleContent>
    <DialogContent>
        <div class="edit-form">
            <MudTextField @bind-Value="_editingAttachment.Description"
                         Label="Description"
                         Variant="Variant.Outlined"
                         Lines="3"
                         FullWidth="true"
                         Placeholder="Enter a description for this attachment..." />
        </div>
    </DialogContent>
    <DialogActions>
        <MudButton OnClick="@(() => _showEditDialog = false)">Cancel</MudButton>
        <MudButton Color="Color.Primary" 
                  Variant="Variant.Filled" 
                  OnClick="@SaveAttachmentEdit">
            Save
        </MudButton>
    </DialogActions>
</MudDialog>

@code {
    [Parameter] public TemplateDto? Template { get; set; }
    [Parameter] public List<MemoAttachmentDto> MemoAttachments { get; set; } = new();
    [Parameter] public EventCallback<List<MemoAttachmentDto>> OnAttachmentsChanged { get; set; }

    private bool _isDragOver = false;
    private List<string> _uploadErrors = new();
    private bool _showEditDialog = false;
    private MemoAttachmentDto _editingAttachment = new();

    private DialogOptions _dialogOptions = new()
    {
        CloseOnEscapeKey = true,
        MaxWidth = MaxWidth.Medium,
        FullWidth = true
    };

    private readonly string[] _allowedExtensions = { ".pdf", ".doc", ".docx", ".xls", ".xlsx", ".png", ".jpg", ".jpeg" };

    private async Task OnFilesSelected(IReadOnlyList<IBrowserFile> files)
    {
        _uploadErrors.Clear();

        if (Template == null || !Template.MemoTemplateAttachmentAllowed)
        {
            _uploadErrors.Add("Attachments are not allowed for this template.");
            return;
        }

        foreach (var file in files)
        {
            if (await ValidateFile(file))
            {
                await ProcessFile(file);
            }
        }

        await OnAttachmentsChanged.InvokeAsync(MemoAttachments);
        StateHasChanged();
    }

    private async Task<bool> ValidateFile(IBrowserFile file)
    {
        // Check file count
        if (MemoAttachments.Count >= Template.MemoTemplateAttachmentFileCountAllowed)
        {
            _uploadErrors.Add($"Maximum number of files ({Template.MemoTemplateAttachmentFileCountAllowed}) exceeded.");
            return false;
        }

        // Check file size
        var maxSizeBytes = (long)(Template.MemoTemplateAttachmentPerFileSizeMbAllowed * 1024 * 1024);
        if (file.Size > maxSizeBytes)
        {
            _uploadErrors.Add($"{file.Name}: File size ({FormatFileSize(file.Size)}) exceeds maximum allowed ({Template.MemoTemplateAttachmentPerFileSizeMbAllowed} MB).");
            return false;
        }

        // Check file extension
        var extension = Path.GetExtension(file.Name).ToLowerInvariant();
        if (!_allowedExtensions.Contains(extension))
        {
            _uploadErrors.Add($"{file.Name}: File type not allowed. Supported formats: {string.Join(", ", _allowedExtensions)}");
            return false;
        }

        // Check for duplicate names
        if (MemoAttachments.Any(a => a.Name.Equals(file.Name, StringComparison.OrdinalIgnoreCase)))
        {
            _uploadErrors.Add($"{file.Name}: A file with this name already exists.");
            return false;
        }

        return true;
    }

    private async Task ProcessFile(IBrowserFile file)
    {
        try
        {
            // In a real implementation, you would upload the file to a server or storage
            // For now, we'll just create the attachment record
            var attachment = new MemoAttachmentDto
            {
                Name = file.Name,
                Type = Path.GetExtension(file.Name).TrimStart('.'),
                Size = FormatFileSize(file.Size),
                Description = "",
                TempId = Guid.NewGuid().ToString(),
                Path = $"temp/{Guid.NewGuid()}_{file.Name}" // Temporary path
            };

            MemoAttachments.Add(attachment);
        }
        catch (Exception ex)
        {
            _uploadErrors.Add($"{file.Name}: Upload failed - {ex.Message}");
        }
    }

    private void OnDragOver()
    {
        _isDragOver = true;
    }

    private void OnDragLeave()
    {
        _isDragOver = false;
    }

    private async Task OnDrop()
    {
        _isDragOver = false;
        // File drop handling is managed by MudFileUpload
    }

    private void EditAttachment(MemoAttachmentDto attachment)
    {
        _editingAttachment = new MemoAttachmentDto
        {
            Name = attachment.Name,
            Type = attachment.Type,
            Size = attachment.Size,
            Description = attachment.Description,
            Path = attachment.Path,
            TempId = attachment.TempId
        };
        _showEditDialog = true;
    }

    private async Task SaveAttachmentEdit()
    {
        var originalAttachment = MemoAttachments.FirstOrDefault(a => a.TempId == _editingAttachment.TempId);
        if (originalAttachment != null)
        {
            originalAttachment.Description = _editingAttachment.Description;
            await OnAttachmentsChanged.InvokeAsync(MemoAttachments);
        }
        _showEditDialog = false;
        StateHasChanged();
    }

    private async Task RemoveAttachment(MemoAttachmentDto attachment)
    {
        MemoAttachments.Remove(attachment);
        await OnAttachmentsChanged.InvokeAsync(MemoAttachments);
        StateHasChanged();
    }

    private string GetFileIcon(string fileType)
    {
        return fileType.ToLowerInvariant() switch
        {
            "pdf" => Icons.Material.Filled.PictureAsPdf,
            "doc" or "docx" => Icons.Material.Filled.Description,
            "xls" or "xlsx" => Icons.Material.Filled.TableChart,
            "png" or "jpg" or "jpeg" => Icons.Material.Filled.Image,
            _ => Icons.Material.Filled.InsertDriveFile
        };
    }

    private Color GetFileIconColor(string fileType)
    {
        return fileType.ToLowerInvariant() switch
        {
            "pdf" => Color.Error,
            "doc" or "docx" => Color.Primary,
            "xls" or "xlsx" => Color.Success,
            "png" or "jpg" or "jpeg" => Color.Info,
            _ => Color.Default
        };
    }

    private string FormatFileSize(long bytes)
    {
        string[] sizes = { "B", "KB", "MB", "GB" };
        double len = bytes;
        int order = 0;
        while (len >= 1024 && order < sizes.Length - 1)
        {
            order++;
            len = len / 1024;
        }
        return $"{len:0.##} {sizes[order]}";
    }
}

<style>
    .document-attachment-step {
        max-width: 1000px;
        margin: 0 auto;
        padding: 2rem;
    }

    .step-header {
        margin-bottom: 2rem;
        text-align: center;
    }

    .step-title {
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 0.5rem;
        font-size: 1.5rem;
        font-weight: 600;
        color: #2c3e50;
        margin-bottom: 0.5rem;
    }

    .step-icon {
        color: #4f8cff;
    }

    .step-description {
        color: #6c757d;
        font-size: 1rem;
        line-height: 1.5;
        margin: 0;
    }

    .step-content {
        background: #fff;
        border-radius: 12px;
        padding: 2rem;
        box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
        border: 1px solid #e9ecef;
    }

    .attachment-rules {
        background: #f8f9fa;
        border: 1px solid #e9ecef;
        border-radius: 8px;
        padding: 1.5rem;
        margin-bottom: 2rem;
    }

    .rules-header {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        font-weight: 600;
        color: #2c3e50;
        margin-bottom: 1rem;
    }

    .rules-content {
        display: flex;
        flex-direction: column;
        gap: 0.5rem;
    }

    .rule-item {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        font-size: 0.9rem;
        color: #495057;
    }

    .rule-item.warning {
        color: #856404;
        background: #fff3cd;
        padding: 0.5rem;
        border-radius: 4px;
        border: 1px solid #ffeaa7;
    }

    .upload-section {
        margin-bottom: 2rem;
    }

    .upload-area {
        border: 2px dashed #dee2e6;
        border-radius: 12px;
        padding: 2rem;
        text-align: center;
        transition: all 0.3s ease;
        background: #fafafa;
    }

    .upload-area:hover,
    .upload-area.drag-over {
        border-color: #4f8cff;
        background: #f0f7ff;
    }

    .upload-content h4 {
        margin: 1rem 0 0.5rem 0;
        color: #2c3e50;
    }

    .upload-content p {
        margin: 0 0 1rem 0;
        color: #6c757d;
    }

    .upload-input {
        width: 100%;
    }

    .upload-errors {
        margin-top: 1rem;
        padding: 1rem;
        background: #f8d7da;
        border: 1px solid #f5c6cb;
        border-radius: 6px;
    }

    .error-item {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        color: #721c24;
        font-size: 0.9rem;
        margin-bottom: 0.5rem;
    }

    .error-item:last-child {
        margin-bottom: 0;
    }

    .attachments-list {
        margin-top: 2rem;
    }

    .list-header {
        margin-bottom: 1rem;
        padding-bottom: 0.5rem;
        border-bottom: 1px solid #e9ecef;
    }

    .list-header h3 {
        margin: 0;
        color: #2c3e50;
        font-weight: 600;
    }

    .attachments-grid {
        display: flex;
        flex-direction: column;
        gap: 1rem;
    }

    .attachment-item {
        display: flex;
        align-items: center;
        gap: 1rem;
        padding: 1rem;
        background: #f8f9fa;
        border: 1px solid #e9ecef;
        border-radius: 8px;
        transition: all 0.2s ease;
    }

    .attachment-item:hover {
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }

    .attachment-icon {
        flex-shrink: 0;
    }

    .attachment-info {
        flex: 1;
        min-width: 0;
    }

    .attachment-name {
        font-weight: 600;
        color: #2c3e50;
        margin-bottom: 0.25rem;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
    }

    .attachment-details {
        font-size: 0.9rem;
        color: #6c757d;
        margin-bottom: 0.25rem;
    }

    .attachment-description {
        font-size: 0.9rem;
        color: #495057;
        font-style: italic;
    }

    .attachment-actions {
        display: flex;
        gap: 0.25rem;
        flex-shrink: 0;
    }

    .no-attachments {
        text-align: center;
        padding: 3rem;
        color: #6c757d;
    }

    .no-attachments h4 {
        margin: 1rem 0 0.5rem 0;
        color: #495057;
    }

    .no-attachments p {
        margin: 0;
        line-height: 1.5;
    }

    .no-template {
        text-align: center;
        padding: 3rem;
        color: #6c757d;
    }

    .no-template h3 {
        margin: 1rem 0 0.5rem 0;
        color: #495057;
    }

    .no-template p {
        margin: 0;
        line-height: 1.5;
    }

    .edit-form {
        padding: 1rem 0;
    }
</style>
