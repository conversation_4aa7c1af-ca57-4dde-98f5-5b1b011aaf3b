@using MudBlazor

<div class="memo-stepper">
    <div class="stepper-container">
        @for (int i = 1; i <= 5; i++)
        {
            var stepNumber = i;
            var isActive = stepNumber == CurrentStep;
            var isCompleted = stepNumber < CurrentStep || (stepNumber == 1 && IsStep1Complete);
            var isClickable = stepNumber == 1 || IsStep1Complete;
            
            <div class="stepper-item @(isActive ? "active" : "") @(isCompleted ? "completed" : "") @(isClickable ? "clickable" : "")"
                 @onclick="@(() => OnStepClick(stepNumber))">
                
                <div class="stepper-circle">
                    @if (isCompleted && stepNumber != CurrentStep)
                    {
                        <MudIcon Icon="@Icons.Material.Filled.Check" Size="Size.Small" />
                    }
                    else
                    {
                        <span>@stepNumber</span>
                    }
                </div>
                
                <div class="stepper-label">
                    <div class="stepper-title">@GetStepTitle(stepNumber)</div>
                    <div class="stepper-subtitle">@GetStepSubtitle(stepNumber)</div>
                </div>
                
                @if (stepNumber < 5)
                {
                    <div class="stepper-connector @(isCompleted ? "completed" : "")"></div>
                }
            </div>
        }
    </div>
</div>

@code {
    [Parameter] public int CurrentStep { get; set; } = 1;
    [Parameter] public bool IsStep1Complete { get; set; } = false;
    [Parameter] public EventCallback<int> OnStepChanged { get; set; }

    private async Task OnStepClick(int step)
    {
        if (step == 1 || IsStep1Complete)
        {
            await OnStepChanged.InvokeAsync(step);
        }
    }

    private string GetStepTitle(int step)
    {
        return step switch
        {
            1 => "Template & Title",
            2 => "Content",
            3 => "Approvers",
            4 => "Attachments",
            5 => "Review & Submit",
            _ => ""
        };
    }

    private string GetStepSubtitle(int step)
    {
        return step switch
        {
            1 => "Select template and enter memo title",
            2 => "Compose memo content",
            3 => "Configure approval workflow",
            4 => "Add supporting documents",
            5 => "Review and submit for approval",
            _ => ""
        };
    }
}

<style>
    .memo-stepper {
        margin: 2rem 0;
        padding: 1.5rem;
        background: #f8f9fa;
        border-radius: 12px;
        border: 1px solid #e9ecef;
    }

    .stepper-container {
        display: flex;
        align-items: flex-start;
        justify-content: space-between;
        position: relative;
    }

    .stepper-item {
        display: flex;
        flex-direction: column;
        align-items: center;
        flex: 1;
        position: relative;
        cursor: default;
        transition: all 0.3s ease;
    }

    .stepper-item.clickable {
        cursor: pointer;
    }

    .stepper-item.clickable:hover .stepper-circle {
        transform: scale(1.1);
        box-shadow: 0 4px 12px rgba(79, 140, 255, 0.3);
    }

    .stepper-circle {
        width: 48px;
        height: 48px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: 600;
        font-size: 16px;
        margin-bottom: 12px;
        transition: all 0.3s ease;
        border: 3px solid #dee2e6;
        background: #fff;
        color: #6c757d;
    }

    .stepper-item.active .stepper-circle {
        background: linear-gradient(135deg, #4f8cff 0%, #1abc9c 100%);
        color: white;
        border-color: #4f8cff;
        box-shadow: 0 4px 16px rgba(79, 140, 255, 0.3);
    }

    .stepper-item.completed .stepper-circle {
        background: #28a745;
        color: white;
        border-color: #28a745;
    }

    .stepper-label {
        text-align: center;
        max-width: 140px;
    }

    .stepper-title {
        font-weight: 600;
        font-size: 14px;
        color: #2c3e50;
        margin-bottom: 4px;
        line-height: 1.2;
    }

    .stepper-subtitle {
        font-size: 12px;
        color: #6c757d;
        line-height: 1.3;
    }

    .stepper-item.active .stepper-title {
        color: #4f8cff;
    }

    .stepper-item.completed .stepper-title {
        color: #28a745;
    }

    .stepper-connector {
        position: absolute;
        top: 24px;
        left: calc(50% + 24px);
        right: calc(-50% + 24px);
        height: 3px;
        background: #dee2e6;
        z-index: 1;
        transition: all 0.3s ease;
    }

    .stepper-connector.completed {
        background: linear-gradient(90deg, #28a745 0%, #4f8cff 100%);
    }

    /* Mobile styles moved to external CSS file */
</style>
