@page "/memo-builder"
@page "/memo-builder/{id:int}"
@using PSSmartMemo.DTO
@inject MemoDataService MemoDataService
@inject TemplateDataService TemplateDataService
@inject AdminDataService AdminDataService
@inject AuthenticationStateProvider AuthenticationStateProvider
@inject NavigationManager NavigationManager
@inject IJSRuntime JSRuntime
@rendermode InteractiveServer

<SfBreadcrumb class="mb-2">
    <BreadcrumbItems>
        <BreadcrumbItem IconCss="e-icons e-home" Text="Home" Url="/"></BreadcrumbItem>
        <BreadcrumbItem Text="Memos" Url="/my-memo-lister"></BreadcrumbItem>
        <BreadcrumbItem Text="Memo Builder" Url=""></BreadcrumbItem>
    </BreadcrumbItems>
</SfBreadcrumb>

<div class="memo-builder-container">
    <div class="memo-builder-header">
        <h1 class="memo-builder-title">
            @if (Id.HasValue)
            {
                <span>Edit Memo</span>
            }
            else
            {
                <span>Create New Memo</span>
            }
        </h1>
    </div>

    <!-- Stepper Component -->
    <PSSmartMemo.Components.Pages.Memo.MemoBuilder.MemoStepper CurrentStep="@_currentStep" 
                 IsStep1Complete="@_isStep1Complete"
                 OnStepChanged="@OnStepChanged" />

    <!-- Step Content -->
    <div class="memo-builder-content">
        @switch (_currentStep)
        {
            case 1:
                <PSSmartMemo.Components.Pages.Memo.MemoBuilder.TemplateSelectionStep @ref="_templateSelectionStep"
                                     OnStepCompleted="@OnStep1Completed"
                                     OnTemplateSelected="@OnTemplateSelected"
                                     OnTitleChanged="@OnTitleChanged"
                                     SelectedTemplateId="@_memoState.TemplateId"
                                     MemoTitle="@_memoState.Title" />
                break;
            case 2:
                <PSSmartMemo.Components.Pages.Memo.MemoBuilder.ContentCompositionStep @ref="_contentCompositionStep"
                                      Template="@_selectedTemplate"
                                      MemoSections="@_memoState.Sections"
                                      OnSectionsChanged="@OnSectionsChanged" />
                break;
            case 3:
                <PSSmartMemo.Components.Pages.Memo.MemoBuilder.ApproverWorkflowStep @ref="_approverWorkflowStep"
                                    Template="@_selectedTemplate"
                                    MemoApprovers="@_memoState.Approvers"
                                    OnApproversChanged="@OnApproversChanged" />
                break;
            case 4:
                <PSSmartMemo.Components.Pages.Memo.MemoBuilder.DocumentAttachmentStep @ref="_documentAttachmentStep"
                                      Template="@_selectedTemplate"
                                      MemoAttachments="@_memoState.Attachments"
                                      OnAttachmentsChanged="@OnAttachmentsChanged" />
                break;
            case 5:
                <PSSmartMemo.Components.Pages.Memo.MemoBuilder.FinalActionsStep @ref="_finalActionsStep"
                                MemoState="@_memoState"
                                Template="@_selectedTemplate"
                                OnSaveAsDraft="@OnSaveAsDraft"
                                OnSubmitForApproval="@OnSubmitForApproval" />
                break;
        }
    </div>

    <!-- Navigation Buttons -->
    <div class="memo-builder-navigation">
        <div class="nav-buttons">
            @if (_currentStep > 1)
            {
                <MudButton Variant="Variant.Outlined" 
                          Color="Color.Default"
                          OnClick="@(() => NavigateToStep(_currentStep - 1))"
                          StartIcon="@Icons.Material.Filled.ArrowBack">
                    Previous
                </MudButton>
            }
            
            <div class="nav-spacer"></div>
            
            @if (_currentStep < 5)
            {
                <MudButton Variant="Variant.Filled" 
                          Color="Color.Primary"
                          OnClick="@(() => NavigateToStep(_currentStep + 1))"
                          Disabled="@(!CanNavigateToNextStep())"
                          EndIcon="@Icons.Material.Filled.ArrowForward">
                    Next
                </MudButton>
            }
        </div>
    </div>
</div>

@code {
    [Parameter] public int? Id { get; set; }
    
    private int _currentStep = 1;
    private bool _isStep1Complete = false;
    private string _currentUserId = "";
    private MemoBuilderState _memoState = new();
    private TemplateDto? _selectedTemplate;
    
    // Step component references
    private PSSmartMemo.Components.Pages.Memo.MemoBuilder.TemplateSelectionStep? _templateSelectionStep;
    private PSSmartMemo.Components.Pages.Memo.MemoBuilder.ContentCompositionStep? _contentCompositionStep;
    private PSSmartMemo.Components.Pages.Memo.MemoBuilder.ApproverWorkflowStep? _approverWorkflowStep;
    private PSSmartMemo.Components.Pages.Memo.MemoBuilder.DocumentAttachmentStep? _documentAttachmentStep;
    private PSSmartMemo.Components.Pages.Memo.MemoBuilder.FinalActionsStep? _finalActionsStep;

    protected override async Task OnInitializedAsync()
    {
        var authState = await AuthenticationStateProvider.GetAuthenticationStateAsync();
        _currentUserId = authState.User.Identity?.Name ?? "";
        
        if (Id.HasValue)
        {
            await LoadExistingMemo(Id.Value);
        }
        else
        {
            _memoState = new MemoBuilderState();
        }
    }

    private async Task LoadExistingMemo(int memoId)
    {
        // Load existing memo data
        var memo = await MemoDataService.GetMemo(memoId);
        if (memo != null)
        {
            _memoState.MemoId = memo.MemoId;
            _memoState.Title = memo.MemoTitle;
            _memoState.TemplateId = memo.MemoTemplateId;
            
            // Load template
            if (_memoState.TemplateId.HasValue)
            {
                _selectedTemplate = await TemplateDataService.GetById(_memoState.TemplateId.Value);
                _isStep1Complete = true;
            }
            
            // Load sections, approvers, attachments
            await LoadMemoDetails(memoId);
        }
    }

    private async Task LoadMemoDetails(int memoId)
    {
        try
        {
            // Load memo sections - use existing method with memo object
            var memo = await MemoDataService.GetMemo(memoId);
            if (memo != null)
            {
                _memoState.Sections = await MemoDataService.GetMemoSections(memoId, memo);
            }
            
            // Load memo attachments
            _memoState.Attachments = await MemoDataService.GetMemoAttachments(memoId);
            
            // Load memo approvers - use a simplified approach for now
            _memoState.Approvers = new List<MemoApproverDto>(); // Will be populated from template defaults
        }
        catch (Exception ex)
        {
            await JSRuntime.InvokeVoidAsync("console.error", $"Error loading memo details: {ex.Message}");
            // Initialize empty collections on error
            _memoState.Sections = new List<MemoSectionDto>();
            _memoState.Approvers = new List<MemoApproverDto>();
            _memoState.Attachments = new List<MemoAttachmentDto>();
        }
    }

    private async Task OnStepChanged(int step)
    {
        if (step == 1 || _isStep1Complete)
        {
            _currentStep = step;
            StateHasChanged();
        }
    }

    private async Task NavigateToStep(int step)
    {
        await OnStepChanged(step);
    }

    private bool CanNavigateToNextStep()
    {
        return _currentStep switch
        {
            1 => _isStep1Complete,
            2 => true, // Content can be saved as draft
            3 => true, // Approvers can be configured later
            4 => true, // Attachments are optional
            _ => false
        };
    }

    private async Task OnStep1Completed(bool isComplete)
    {
        _isStep1Complete = isComplete;
        StateHasChanged();
    }

    private async Task OnTemplateSelected(int? templateId)
    {
        _memoState.TemplateId = templateId;
        if (templateId.HasValue)
        {
            _selectedTemplate = await TemplateDataService.GetById(templateId.Value);
            
            // Initialize sections based on template
            if (_selectedTemplate?.Sections != null)
            {
                _memoState.Sections = _selectedTemplate.Sections.Select(s => new MemoSectionDto
                {
                    MemoTemplateSectionId = s.TemplateSectionId,
                    Section = s.SectionTitle,
                    TemplateSectionTitle = s.SectionTitle,
                    TemplateContentHtml = s.ContentHtml,
                    IsRequired = s.IsRequired,
                    SectionSortOrder = s.SectionSortOrder,
                    Content = "",
                    ContentHtml = s.ContentHtml
                }).ToList();
            }
            
            // Initialize default approvers based on template
            if (_selectedTemplate?.Approvers != null)
            {
                _memoState.Approvers = _selectedTemplate.Approvers.Select(a => new MemoApproverDto
                {
                    MemoApproverRoleId = a.MemoApproverRoleId,
                    Role = a.ApproverRoleTitle,
                    AllowType = a.AllowType,
                    SortOrder = 1
                }).ToList();
            }
        }
        StateHasChanged();
    }

    private async Task OnTitleChanged(string title)
    {
        _memoState.Title = title;
        StateHasChanged();
    }

    private async Task OnSectionsChanged(List<MemoSectionDto> sections)
    {
        _memoState.Sections = sections;
        await AutoSaveIfNeeded();
        StateHasChanged();
    }

    private async Task OnApproversChanged(List<MemoApproverDto> approvers)
    {
        _memoState.Approvers = approvers;
        StateHasChanged();
    }

    private async Task OnAttachmentsChanged(List<MemoAttachmentDto> attachments)
    {
        _memoState.Attachments = attachments;
        await AutoSaveIfNeeded();
        StateHasChanged();
    }

    private async Task AutoSaveIfNeeded()
    {
        // Auto-save logic - save as draft every few minutes or when significant changes occur
        if (_memoState.MemoId != -1 && !string.IsNullOrWhiteSpace(_memoState.Title) && _memoState.TemplateId.HasValue)
        {
            try
            {
                // Only auto-save if we have a valid memo with title and template
                // This could be enhanced with a timer-based approach
                await Task.Delay(1); // Placeholder for actual auto-save logic
            }
            catch (Exception ex)
            {
                // Log error but don't interrupt user workflow
                await JSRuntime.InvokeVoidAsync("console.warn", $"Auto-save failed: {ex.Message}");
            }
        }
    }

    private async Task OnSaveAsDraft()
    {
        try
        {
            // Validate minimum requirements for draft
            if (string.IsNullOrWhiteSpace(_memoState.Title))
            {
                await JSRuntime.InvokeVoidAsync("alert", "Please enter a memo title before saving.");
                return;
            }

            if (!_memoState.TemplateId.HasValue)
            {
                await JSRuntime.InvokeVoidAsync("alert", "Please select a template before saving.");
                return;
            }

            var memoDto = new MemoDto
            {
                MemoId = _memoState.MemoId,
                MemoTitle = _memoState.Title,
                MemoTemplateId = _memoState.TemplateId,
                MemoTypeId = _selectedTemplate?.MemoTypeId
            };

            var result = await MemoDataService.SaveMemo(
                _memoState.MemoId,
                memoDto,
                _memoState.Sections,
                _memoState.Attachments,
                _memoState.Approvers,
                _currentUserId,
                "DRAFT",
                false
            );

            if (result > 0)
            {
                _memoState.MemoId = result;
                await JSRuntime.InvokeVoidAsync("alert", "Memo saved as draft successfully!");
                NavigationManager.NavigateTo("/my-memo-lister");
            }
            else
            {
                await JSRuntime.InvokeVoidAsync("alert", "Failed to save memo. Please try again.");
            }
        }
        catch (Exception ex)
        {
            await JSRuntime.InvokeVoidAsync("alert", $"Error saving memo: {ex.Message}");
        }
    }

    private async Task OnSubmitForApproval()
    {
        try
        {
            // Validate all requirements for submission
            var validationErrors = ValidateForSubmission();
            if (validationErrors.Any())
            {
                var errorMessage = "Please fix the following issues before submitting:\n" + string.Join("\n", validationErrors);
                await JSRuntime.InvokeVoidAsync("alert", errorMessage);
                return;
            }

            var memoDto = new MemoDto
            {
                MemoId = _memoState.MemoId,
                MemoTitle = _memoState.Title,
                MemoTemplateId = _memoState.TemplateId,
                MemoTypeId = _selectedTemplate?.MemoTypeId
            };

            var result = await MemoDataService.SaveMemo(
                _memoState.MemoId,
                memoDto,
                _memoState.Sections,
                _memoState.Attachments,
                _memoState.Approvers,
                _currentUserId,
                "PUBLISHED",
                false
            );

            if (result > 0)
            {
                await JSRuntime.InvokeVoidAsync("alert", "Memo submitted for approval successfully!");
                NavigationManager.NavigateTo("/my-memo-lister");
            }
            else
            {
                await JSRuntime.InvokeVoidAsync("alert", "Failed to submit memo. Please try again.");
            }
        }
        catch (Exception ex)
        {
            await JSRuntime.InvokeVoidAsync("alert", $"Error submitting memo: {ex.Message}");
        }
    }

    private List<string> ValidateForSubmission()
    {
        var errors = new List<string>();

        if (string.IsNullOrWhiteSpace(_memoState.Title))
            errors.Add("• Memo title is required");

        if (!_memoState.TemplateId.HasValue)
            errors.Add("• Template selection is required");

        var requiredSections = _memoState.Sections.Where(s => s.IsRequired && string.IsNullOrWhiteSpace(s.ContentHtml)).ToList();
        if (requiredSections.Any())
            errors.Add($"• {requiredSections.Count} required section(s) are empty");

        var incompleteApprovers = _memoState.Approvers.Where(a => string.IsNullOrWhiteSpace(a.UserId)).ToList();
        if (incompleteApprovers.Any())
            errors.Add($"• {incompleteApprovers.Count} approver(s) do not have users assigned");

        return errors;
    }

    // State management class
    public class MemoBuilderState
    {
        public int MemoId { get; set; } = -1;
        public string Title { get; set; } = "";
        public int? TemplateId { get; set; }
        public List<MemoSectionDto> Sections { get; set; } = new();
        public List<MemoApproverDto> Approvers { get; set; } = new();
        public List<MemoAttachmentDto> Attachments { get; set; } = new();
    }
}
